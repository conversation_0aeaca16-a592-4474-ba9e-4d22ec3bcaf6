import json
from collections import defaultdict

def detailed_signal_analysis():
    """Detailed analysis focusing on the specific problems mentioned"""
    
    # Load data
    with open('signals/signals_2025-07-26.json', 'r') as f:
        data = json.load(f)
    
    # Filter OTC pairs only
    otc_signals = [signal for signal in data if '_otc' in signal['pair']]
    
    print("🔍 COMPREHENSIVE SIGNALS ANALYSIS FOR OTC PAIRS")
    print("="*70)
    
    # Basic statistics
    total_signals = len(otc_signals)
    wins = sum(1 for s in otc_signals if s['result'] == 'win')
    losses = sum(1 for s in otc_signals if s['result'] == 'loss')
    
    print(f"📊 OVERALL STATISTICS:")
    print(f"   Total OTC Signals: {total_signals}")
    print(f"   Wins: {wins} ({wins/total_signals*100:.1f}%)")
    print(f"   Losses: {losses} ({losses/total_signals*100:.1f}%)")
    print(f"   Current Accuracy: {wins/total_signals*100:.1f}%")
    print()
    
    # Accuracy by pair
    pair_stats = defaultdict(lambda: {'wins': 0, 'losses': 0})
    for signal in otc_signals:
        pair = signal['pair']
        if signal['result'] == 'win':
            pair_stats[pair]['wins'] += 1
        else:
            pair_stats[pair]['losses'] += 1
    
    print("📈 ACCURACY BY PAIR:")
    for pair, stats in sorted(pair_stats.items()):
        total = stats['wins'] + stats['losses']
        accuracy = stats['wins'] / total * 100
        print(f"   {pair}: {stats['wins']}/{total} = {accuracy:.1f}% accuracy")
    print()
    
    # Analyze the specific problem: small green candles causing PUT losses
    print("🚨 ANALYZING THE SMALL GREEN CANDLE PROBLEM:")
    
    # Count signals where PUT direction lost due to small upward price movement
    problematic_signals = []
    very_small_movements = []
    
    for signal in otc_signals:
        if signal.get('final_price') is not None:
            price_change = signal['final_price'] - signal['price']
            price_change_pct = (price_change / signal['price']) * 100
            
            # PUT trade that lost due to small upward movement (small green candle)
            if (signal['direction'] == 'put' and 
                signal['result'] == 'loss' and 
                price_change > 0 and 
                abs(price_change_pct) < 0.1):  # Very small movement
                
                problematic_signals.append({
                    'pair': signal['pair'],
                    'timestamp': signal['timestamp'],
                    'price': signal['price'],
                    'final_price': signal['final_price'],
                    'change_pct': price_change_pct,
                    'rsi': signal['indicators']['rsi']
                })
                
                if abs(price_change_pct) < 0.05:  # Extremely small
                    very_small_movements.append(signal)
    
    print(f"   PUT trades lost to small green candles: {len(problematic_signals)}")
    print(f"   Extremely small movements (< 0.05%): {len(very_small_movements)}")
    
    if problematic_signals:
        avg_change = sum(s['change_pct'] for s in problematic_signals) / len(problematic_signals)
        avg_rsi = sum(s['rsi'] for s in problematic_signals) / len(problematic_signals)
        print(f"   Average price change in these losses: {avg_change:.4f}%")
        print(f"   Average RSI in these losses: {avg_rsi:.1f}")
    print()
    
    # What happens if we filter out these problematic trades?
    print("💡 IMPACT OF FILTERING SMALL MOVEMENTS:")
    
    # Filter 1: Remove trades with < 0.05% movement
    filter1_signals = []
    for signal in otc_signals:
        if signal.get('final_price') is not None:
            price_change_pct = abs(signal['final_price'] - signal['price']) / signal['price'] * 100
            if price_change_pct >= 0.05:
                filter1_signals.append(signal)
    
    if filter1_signals:
        f1_wins = sum(1 for s in filter1_signals if s['result'] == 'win')
        f1_total = len(filter1_signals)
        f1_accuracy = f1_wins / f1_total * 100
        print(f"   Filter 1 (>= 0.05% movement):")
        print(f"     Remaining signals: {f1_total} (removed {total_signals - f1_total})")
        print(f"     New accuracy: {f1_accuracy:.1f}% (improvement: +{f1_accuracy - wins/total_signals*100:.1f}%)")
    
    # Filter 2: Remove trades with < 0.1% movement
    filter2_signals = []
    for signal in otc_signals:
        if signal.get('final_price') is not None:
            price_change_pct = abs(signal['final_price'] - signal['price']) / signal['price'] * 100
            if price_change_pct >= 0.1:
                filter2_signals.append(signal)
    
    if filter2_signals:
        f2_wins = sum(1 for s in filter2_signals if s['result'] == 'win')
        f2_total = len(filter2_signals)
        f2_accuracy = f2_wins / f2_total * 100
        print(f"   Filter 2 (>= 0.1% movement):")
        print(f"     Remaining signals: {f2_total} (removed {total_signals - f2_total})")
        print(f"     New accuracy: {f2_accuracy:.1f}% (improvement: +{f2_accuracy - wins/total_signals*100:.1f}%)")
    print()
    
    # Analyze RSI patterns in losses
    print("📉 RSI ANALYSIS IN LOSSES:")
    
    oversold_losses = []  # RSI < 20 but PUT still lost
    for signal in otc_signals:
        if (signal['direction'] == 'put' and 
            signal['result'] == 'loss' and 
            signal['indicators']['rsi'] < 20):
            oversold_losses.append(signal)
    
    print(f"   PUT losses in oversold conditions (RSI < 20): {len(oversold_losses)}")
    print(f"   This represents {len(oversold_losses)/losses*100:.1f}% of all losses")
    print()
    
    # Solutions and recommendations
    print("🛠️  SOLUTIONS TO SOLVE THE SMALL GREEN CANDLE PROBLEM:")
    print()
    print("1. 📏 MINIMUM MOVEMENT FILTER:")
    print("   - Only trade when expected price movement > 0.1%")
    print("   - This would eliminate most problematic trades")
    print()
    print("2. ⏰ CANDLE CONFIRMATION:")
    print("   - Wait for candle to close before entering")
    print("   - Don't trade on wicks or temporary movements")
    print()
    print("3. 📊 VOLATILITY FILTER:")
    print("   - Use ATR (Average True Range) to measure volatility")
    print("   - Only trade when ATR > minimum threshold")
    print("   - Check Bollinger Band width for volatility")
    print()
    print("4. 🎯 MULTIPLE TIMEFRAME CONFIRMATION:")
    print("   - Check higher timeframe (5M, 15M) for trend direction")
    print("   - Ensure alignment between timeframes")
    print()
    print("5. 💪 TREND STRENGTH FILTER:")
    print("   - Use ADX indicator to measure trend strength")
    print("   - Only trade when ADX > 25 (strong trend)")
    print()
    print("6. 🚫 AVOID LOW VOLATILITY PERIODS:")
    print("   - Don't trade during market open/close")
    print("   - Avoid trading during news events")
    print("   - Skip trades when market is ranging")
    print()
    
    # Calculate potential improvement
    if filter2_signals:
        potential_improvement = f2_accuracy - (wins/total_signals*100)
        print(f"🎯 POTENTIAL RESULTS:")
        print(f"   Current accuracy: {wins/total_signals*100:.1f}%")
        print(f"   With 0.1% movement filter: {f2_accuracy:.1f}%")
        print(f"   Potential improvement: +{potential_improvement:.1f}%")
        print(f"   From {f2_total} signals, expect {int(f2_total * f2_accuracy/100)} wins")

if __name__ == "__main__":
    detailed_signal_analysis()
