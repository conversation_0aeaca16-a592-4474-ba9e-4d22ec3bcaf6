#!/usr/bin/env python3
"""
Quotex Trading Model - Secure Access System
Comprehensive trading bot with secure authentication
Owner: Muhammad Uzair
Model: 2.0.0
"""

import sys
import os
import time
import asyncio
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from contextlib import redirect_stderr
from io import StringIO
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# Import authentication system
from auth_ui import auth_ui

class SuppressOutput:
    """Context manager to suppress stdout and stderr"""
    def __enter__(self):
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr
        sys.stdout = StringIO()
        sys.stderr = StringIO()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self._original_stdout
        sys.stderr = self._original_stderr
        return True  # Suppress any exceptions

# Import PyQuotex integration
try:
    from quotex_integration import QuotexBotIntegration, get_quotex_client
    QUOTEX_AVAILABLE = True
    print("✅ PyQuotex integration loaded successfully")
except ImportError as e:
    print(f"❌ PyQuotex integration not found: {e}")
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price, fetch_live_candles, get_timeframe_time_info
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, OANDA_CONFIG, TIMEFRAME_CONFIG
from signal_logger import (
    initialize_signal_logger, save_signal, evaluate_last_signal,
    update_summary, print_summary_box, get_current_indicators, clear_old_signals
)

# Quotex credentials and URLs (Updated credentials)
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"
QUOTEX_LIVE_URL = "https://market-qx.pro/en/trade"  # Official Quotex URL
QUOTEX_DEMO_URL = "https://market-qx.pro/en/demo-trade"

# Quotex supported assets
QUOTEX_OTC_PAIRS = [
    # Major Currency Pairs
    "EURUSD_otc", "GBPUSD_otc", "USDJPY_otc", "AUDUSD_otc", "USDCAD_otc", "USDCHF_otc",
    "AUDCAD_otc", "AUDCHF_otc", "AUDJPY_otc", "CADJPY_otc", "EURCHF_otc", "EURGBP_otc",
    "EURJPY_otc", "GBPAUD_otc", "GBPJPY_otc", "NZDJPY_otc", "NZDUSD_otc",

    # Exotic Currency Pairs (User requested)
    "USDBDT_otc", "USDARS_otc", "USDBRL_otc", "USDCLP_otc", "USDCOP_otc", "USDEGP_otc",
    "USDILS_otc", "USDINR_otc", "USDKRW_otc", "USDMXN_otc", "USDNGN_otc", "USDPKR_otc",
    "USDTHB_otc", "USDTRY_otc", "USDVND_otc", "USDZAR_otc",

    # Precious Metals
    "XAGUSD_otc", "XAUUSD_otc", "XPDUSD_otc", "XPTUSD_otc",

    # Energy
    "UKBrent_otc", "USCrude_otc", "NATGAS_otc",

    # Major Stocks
    "AXP_otc", "BA_otc", "FB_otc", "INTC_otc", "JNJ_otc", "MCD_otc", "MSFT_otc", "PFE_otc",
    "AAPL_otc", "AMZN_otc", "GOOGL_otc", "NFLX_otc", "TSLA_otc", "NVDA_otc"
]

QUOTEX_LIVE_PAIRS = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURAUD",
    "EURCAD", "EURCHF", "EURGBP", "EURJPY", "EURSGD", "GBPAUD", "GBPCAD", "GBPCHF",
    "GBPJPY", "XAGUSD", "XAUUSD", "DJIUSD", "F40EUR", "FTSGBP", "GEREUR", "HSIHKD",
    "IBXEUR", "IT4EUR", "JPXJPY", "NDXUSD", "SPXUSD", "STXEUR"
]

# Available timeframes for Quotex
QUOTEX_TIMEFRAMES = {
    "1": {"name": "1 Minute", "seconds": 60},
    "2": {"name": "2 Minutes", "seconds": 120},
    "5": {"name": "5 Minutes", "seconds": 300},
    "10": {"name": "10 Minutes", "seconds": 600},
    "15": {"name": "15 Minutes", "seconds": 900},
    "30": {"name": "30 Minutes", "seconds": 1800},
    "60": {"name": "1 Hour", "seconds": 3600}
}

# Global Quotex client
quotex_client = None

def show_menu():
    """Display the main menu"""
    print_header("🚀 QUOTEX TRADING MODEL")
    print_colored("Choose an option:", "SKY_BLUE", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "SUCCESS", bold=True)
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "DARK_MULBERRY", bold=True)
    print_colored("3. 💰 Quotex Live (Live trading)", "BURNT_ORANGE", bold=True)
    print_colored("4. 💳 Check Quotex Balance", "TROPICAL_RAINFOREST", bold=True)
    print_colored("5. ❌ Exit", "ERROR", bold=True)
    print()

async def check_quotex_balance():
    """Check and display Quotex account balance"""
    print_header("💳 QUOTEX BALANCE CHECK")

    if not QUOTEX_AVAILABLE:
        print_colored("❌ PyQuotex integration not available", "ERROR")
        return

    try:
        print_colored("🔗 Connecting to Quotex...", "SUCCESS")

        # Create client instance
        client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=True)

        # Connect
        connected = await client.connect()

        if connected:
            print_colored("✅ Connected to Quotex successfully", "SUCCESS")

            # Get current balance without switching modes
            current_balance = await client.get_balance()
            print_colored(f"💰 Current balance: ${current_balance:.2f}", "SUCCESS" if current_balance > 0 else "WARNING")
            
        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")

    print()
    input("Press Enter to continue...")

async def connect_to_quotex(account_type="PRACTICE", max_retries=5):
    """Connect to Quotex using PyQuotex integration with retry mechanism"""
    global quotex_client

    if not QUOTEX_AVAILABLE:
        print_colored("❌ PyQuotex integration not available", "ERROR")
        return False

    try:
        print_colored("� Connecting to Quotex...", "SUCCESS")

        # Create client instance
        quotex_client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=(account_type in ["PRACTICE", "DEMO"]))

        # Connect
        connected = await quotex_client.connect()

        if connected:
            print_colored("✅ Connected to Quotex successfully", "SUCCESS")

            # Set account type with instant mode switching
            await quotex_client.change_account(account_type)

            # Show practice mode message for option 1
            if account_type == "PRACTICE":
                print_colored("📡 Only provide signal no trade execution", "PEACH", bold=True)
                print_colored("📊 Practice mode: Signal display only (connected to Quotex for OTC data)", "GOLD")

            return True
        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")
            return False

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")
        return False

async def check_balance():
    """Check current account balance"""
    if not quotex_client:
        return 0

    try:
        balance = await quotex_client.get_balance()
        return balance if balance else 0
    except Exception as e:
        print_colored(f"❌ Error checking balance: {str(e)}", "ERROR")
        return 0

async def set_initial_trade_amount(amount):
    """Set trade amount immediately after connection establishment"""
    if not quotex_client:
        print_colored("❌ Quotex client not available for amount setting", "ERROR")
        return False

    try:
        success = await quotex_client._set_trade_amount_immediate(amount)

        if success:
            return True
        else:
            return False

    except Exception as e:
        return False

async def set_initial_trade_time(time_str):
    """Set trade time immediately after time selection"""
    if not quotex_client:
        print_colored("❌ Quotex client not available for time setting", "ERROR")
        return False

    try:
        success = await quotex_client._set_trade_time_immediate(time_str)

        if success:
            return True
        else:
            return False

    except Exception as e:
        return False

async def validate_and_set_trade_amount(expected_amount, last_set_amount):
    """Fast validation and setting of trade amount before trade execution"""
    try:
        # Only update if amount is different (avoid redundant calls)
        if abs(expected_amount - last_set_amount) > 0.01:  # 1 cent tolerance
            success = await set_initial_trade_amount(expected_amount)
            if success:
                return expected_amount  # Return new amount if successful
            else:
                return last_set_amount  # Keep old amount if failed
        else:
            return last_set_amount  # No change needed
    except Exception:
        return last_set_amount  # Keep old amount on error

async def execute_trade(asset, action, amount, duration):
    """Execute trade on Quotex with clean output"""
    if not quotex_client:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

    try:
        # Execute trade (amount should already be set)
        success, trade_info = await quotex_client.trade(action, amount, asset, duration)
        return success, trade_info

    except Exception as e:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

def display_pairs_in_columns(pairs, title, columns=4, start_index=0):
    """Display trading pairs in specified number of columns"""
    print_colored(f"\n{title}", "OCEAN", bold=True)
    print_colored("=" * 80, "SKY_BLUE")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<18}"
        print_colored(formatted_row, "ROSEWOOD")

    print_colored("=" * 80, "SKY_BLUE")

def select_trading_pairs():
    """Select multiple trading pairs from available options"""
    print_header("💱 ASSET SELECTION")

    # Display Live pairs first
    display_pairs_in_columns(QUOTEX_LIVE_PAIRS, "🌍 Live Pairs (Market Hours):", start_index=0)

    # Display OTC pairs with correct numbering
    live_count = len(QUOTEX_LIVE_PAIRS)
    display_pairs_in_columns(QUOTEX_OTC_PAIRS, "📊 OTC Pairs (24/7 Available):", columns=4, start_index=live_count)

    total_pairs = len(QUOTEX_LIVE_PAIRS) + len(QUOTEX_OTC_PAIRS)
    all_pairs = QUOTEX_LIVE_PAIRS + QUOTEX_OTC_PAIRS

    print_colored(f"\n🔸 Select pairs (1,2,3 or 'all' for all pairs):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input(f"\nSelect assets (1-{total_pairs}): ").strip().lower()
            if not selection:
                return None

            if selection == 'all':
                selected_pairs = all_pairs.copy()
                break

            # Parse selection
            selected_pairs = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    # Range selection (e.g., 1-4)
                    start, end = map(int, part.split('-'))
                    for i in range(start-1, min(end, total_pairs)):
                        if i >= 0:
                            selected_pairs.append(all_pairs[i])
                else:
                    # Single selection
                    num = int(part)
                    if 1 <= num <= total_pairs:
                        selected_pairs.append(all_pairs[num-1])
                    else:
                        raise ValueError(f"Invalid asset number: {num}")

            # Remove duplicates while preserving order
            selected_pairs = list(dict.fromkeys(selected_pairs))

            if selected_pairs:
                break
            else:
                print_colored("❌ Please select at least one asset", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected pairs
    print_colored(f"\n✅ Selected {len(selected_pairs)} assets:", "SUCCESS", bold=True)
    for pair in selected_pairs:
        print_colored(f"   • {pair}", "SUCCESS")

    return selected_pairs

def select_timeframe():
    """Select trading timeframe"""
    print_header("⏰ TIMEFRAME SELECTION")

    print_colored("Available timeframes:", "OCEAN", bold=True)
    for key, info in QUOTEX_TIMEFRAMES.items():
        print_colored(f"{key}. {info['name']}", "ROSEWOOD")

    print_colored("\nSelect timeframe (1-7):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTimeframe: ").strip()
            if choice in QUOTEX_TIMEFRAMES:
                selected = QUOTEX_TIMEFRAMES[choice]
                print_colored(f"✅ Selected: {selected['name']}", "SUCCESS")
                return int(choice) * 60  # Return duration in seconds
            else:
                print_colored("❌ Please enter a valid timeframe number", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_time():
    """Select trade time (optional)"""
    print_header("⏰ TRADE TIME SELECTION")

    print_colored("Set trade time (optional):", "OCEAN", bold=True)
    print_colored("Format: HH:MM:SS", "TYRIAN_PURPLE")
    print_colored("Press Enter to keep default time on site", "TYRIAN_PURPLE")

    while True:
        try:
            time_input = input("\nTrade time (HH:MM:SS or Enter for default): ").strip()

            if not time_input:
                print_colored("✅ Using default time from site", "SUCCESS")
                return None

            # Validate time format
            time_parts = time_input.split(':')
            if len(time_parts) != 3:
                print_colored("❌ Invalid format. Use HH:MM:SS", "ERROR")
                continue

            try:
                hours = int(time_parts[0])
                minutes = int(time_parts[1])
                seconds = int(time_parts[2])

                # Validate ranges
                if not (0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59):
                    print_colored("❌ Invalid time values. Hours: 0-23, Minutes/Seconds: 0-59", "ERROR")
                    continue

                # Convert to total seconds for validation
                total_seconds = hours * 3600 + minutes * 60 + seconds

                # Format time first so we can use it in messages
                formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                # Allow any time including less than 1 minute (e.g., 00:00:55)
                if total_seconds < 30:
                    print_colored(f"⚠️ Time {formatted_time} is very short - minimum recommended is 30 seconds", "WARNING")
                elif total_seconds < 60:
                    print_colored(f"✅ Time {formatted_time} accepted (less than 1 minute)", "INFO")

                if total_seconds > 3600:
                    print_colored("❌ Maximum trade time is 1 hour (01:00:00)", "ERROR")
                    continue

                print_colored(f"✅ Trade time: {formatted_time}", "SUCCESS")
                return formatted_time

            except ValueError:
                print_colored("❌ Invalid time format. Use numbers only", "ERROR")
                continue

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_amount():
    """Select trade amount"""
    print_header("💰 TRADE AMOUNT SELECTION")

    print_colored("Available trade amounts:", "OCEAN", bold=True)
    amounts = ["1", "2", "5", "10", "20", "50", "100"]
    for i, amount in enumerate(amounts, 1):
        print_colored(f"{i}. ${amount}", "ROSEWOOD")
    print_colored(f"{len(amounts) + 1}. Custom Amount", "ROSEWOOD")

    print_colored("\nSelect trade amount (1,2,3 or 'custom' for custom amount):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTrade amount: ").strip().lower()
            if not choice:
                return None

            if choice.isdigit() and 1 <= int(choice) <= len(amounts):
                amount = float(amounts[int(choice) - 1])
                # Convert to int if it's a whole number (fix decimal issue)
                if amount.is_integer():
                    amount = int(amount)
                print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                return amount
            elif choice.isdigit() and int(choice) == len(amounts) + 1:
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        # Convert to int if it's a whole number (fix decimal issue)
                        if amount.is_integer():
                            amount = int(amount)
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            elif choice == 'custom':
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        # Convert to int if it's a whole number (fix decimal issue)
                        if amount.is_integer():
                            amount = int(amount)
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            else:
                try:
                    amount = float(choice)
                    if amount < 1:
                        print_colored("❌ Minimum trade amount is $1", "ERROR")
                        continue
                    elif amount > 1000:
                        print_colored("❌ Maximum trade amount is $1000", "ERROR")
                        continue
                    # Convert to int if it's a whole number (fix decimal issue)
                    if amount.is_integer():
                        amount = int(amount)
                    print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                    return amount
                except ValueError:
                    print_colored("❌ Please enter a valid number or selection", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_keep_trying_config():
    """Configure Keep Trying Until Win feature"""
    print_header("🎯 KEEP TRYING UNTIL WIN")

    print_colored("Enable Keep Trying Until Win?", "OCEAN", bold=True)
    print_colored("• Automatically increase trade amount after losses", "GOLD")
    print_colored("• Reset to base amount after a win", "GOLD")
    print()

    while True:
        try:
            choice = input("Enable Keep Trying? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                # Get number of steps
                while True:
                    try:
                        num_steps = int(input("Number of steps (2-10): "))
                        if 2 <= num_steps <= 10:
                            break
                        else:
                            print_colored("❌ Steps must be between 2 and 10", "ERROR")
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")

                # Get step amounts
                step_amounts = []
                print_colored(f"Enter trade amount for each step:", "OCEAN", bold=True)
                for i in range(num_steps):
                    while True:
                        try:
                            step_amount = float(input(f"Step {i+1} amount ($): "))
                            if step_amount > 0:
                                step_amounts.append(step_amount)
                                break
                            else:
                                print_colored("❌ Amount must be positive", "ERROR")
                        except ValueError:
                            print_colored("❌ Please enter a valid number", "ERROR")

                print_colored(f"✅ Keep Trying configured: {num_steps} steps", "SUCCESS")
                return True, step_amounts

            elif choice in ['n', 'no']:
                return False, []
            else:
                print_colored("❌ Please enter 'y' or 'n'", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return False, []

def update_keep_trying_amount(keep_trying_enabled, step_amounts, current_step, consecutive_losses, base_trade_amount, trade_result):
    """Update trade amount based on Keep Trying Until Win logic"""
    if not keep_trying_enabled or not step_amounts:
        return base_trade_amount, 0, 0  # Return base amount if not enabled

    if trade_result == "win":
        # Reset to base amount after a win
        return base_trade_amount, 0, 0
    elif trade_result == "loss":
        # Increase step after a loss
        new_consecutive_losses = consecutive_losses + 1
        new_current_step = min(new_consecutive_losses, len(step_amounts))

        if new_current_step <= len(step_amounts):
            # Use step amount (1-indexed, so subtract 1)
            new_amount = step_amounts[new_current_step - 1]
        else:
            # All steps exhausted, reset to base amount
            new_amount = base_trade_amount
            new_current_step = 0
            new_consecutive_losses = 0

        return new_amount, new_current_step, new_consecutive_losses
    else:
        # No result yet, keep current amount
        current_amount = step_amounts[current_step - 1] if current_step > 0 and current_step <= len(step_amounts) else base_trade_amount
        return current_amount, current_step, consecutive_losses

def select_data_requirement():
    """Interactive data requirement selection"""
    print_header("📊 DATA REQUIREMENT SELECTION")
    print_colored("Select minimum historical candles needed to generate signals:", "OCEAN", bold=True)
    print_colored("• More candles means higher accuracy but slower processing", "GOLD")
    print_colored("• Fewer candles means Faster processing but may reduce accuracy", "GOLD")
    print_colored("• Includes current running candle + historical candles", "GOLD")
    print()

    print_colored("Available options:", "OCEAN", bold=True)
    print_colored("  03-20 candles:   Ultra-fast processing", "TYRIAN_PURPLE")
    print_colored("  21-50 candles:   Balanced speed and accuracy (recommended)", "TYRIAN_PURPLE")
    print_colored("  51-100 candles:  Higher accuracy", "TYRIAN_PURPLE")
    print_colored("  101-200 candles: Maximum accuracy", "TYRIAN_PURPLE")
    print()
    print_colored("💱Default: 15 candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter minimum candles required (3 to 200) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored("✅ Selected: 15 candles minimum (current + 14 historical)", "SUCCESS")
                return 15

            candles = int(user_input)
            if 3 <= candles <= 200:
                print_colored(f"✅ Selected: {candles} candles minimum (current + {candles-1} historical)", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 3 and 200", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 15

def select_data_fetch():
    """Interactive data fetch selection"""
    print_header("📈 DATA FETCH SELECTION")
    print_colored("Select number of candles to fetch from market:", "OCEAN", bold=True)
    print_colored("• More candles means Better context but slower fetching", "GOLD")
    print_colored("• Fewer candles means Faster fetching but less market context", "GOLD")
    print_colored("• Includes current running candle + historical candles", "GOLD")
    print()

    print_colored("Available options:", "OCEAN", bold=True)
    print_colored("  20-50 candles:   Ultra-fast fetching (good for quick signals)", "TYRIAN_PURPLE")
    print_colored("  51-100 candles:  Balanced speed and context (recommended)", "TYRIAN_PURPLE")
    print_colored("  101-250 candles: High context (good for trend analysis)", "TYRIAN_PURPLE")
    print_colored("  251-500 candles: Maximum context (comprehensive analysis)", "TYRIAN_PURPLE")
    print()
    print_colored("⚠️  Default: 50 candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter candles to fetch (20-500) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored("✅ Selected: 50 candles fetch (current + 49 historical)", "SUCCESS")
                return 50

            candles = int(user_input)
            if 20 <= candles <= 500:
                print_colored(f"✅ Selected: {candles} candles fetch (current + {candles-1} historical)", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 20 and 500", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 50

def configure_echo_sniper_strategy():
    """Configure Echo Sniper Strategy parameters"""
    print_header("🎯 ECHO SNIPER STRATEGY CONFIGURATION")

    print_colored("🔧 Strategy Name: Echo Sniper", "SUCCESS", bold=True)
    print()

    # Import default config
    from config import ECHO_SNIPER_CONFIG
    config = ECHO_SNIPER_CONFIG.copy()

    # 1. Pattern Length Configuration
    print_colored("1️⃣ PATTERN LENGTH CONFIGURATION", "OCEAN", bold=True)
    print_colored(f"   ⚠️  Default: {config['pattern_length']} candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter pattern length (2-10) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored(f"✅ Selected: {config['pattern_length']} candles for pattern", "SUCCESS")
                break

            pattern_length = int(user_input)
            if 2 <= pattern_length <= 10:
                config['pattern_length'] = pattern_length
                print_colored(f"✅ Selected: {pattern_length} candles for pattern", "SUCCESS")
                break
            else:
                print_colored("❌ Please enter a number between 2 and 10", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Configuration cancelled", "WARNING")
            return None

    print()

    # 2. Historical Candles Configuration
    print_colored("2️⃣ HISTORICAL CANDLES TO ANALYZE", "OCEAN", bold=True)
    print_colored(f"   ⚠️  Default: {config['historical_candles']} candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter historical candles to analyze (10-300) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored(f"✅ Selected: {config['historical_candles']} historical candles", "SUCCESS")
                break

            historical_candles = int(user_input)
            if 10 <= historical_candles <= 300:
                config['historical_candles'] = historical_candles
                print_colored(f"✅ Selected: {historical_candles} historical candles", "SUCCESS")
                break
            else:
                print_colored("❌ Please enter a number between 10 and 300", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Configuration cancelled", "WARNING")
            return None

    print()

    # 3. Fetch Candles Configuration
    print_colored("3️⃣ NUMBER OF CANDLES TO FETCH FROM MARKET", "OCEAN", bold=True)
    print_colored(f"   ⚠️  Default: {config['fetch_candles']} candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter candles to fetch for accuracy testing (20-500) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored(f"✅ Selected: {config['fetch_candles']} candles for accuracy testing", "SUCCESS")
                break

            fetch_candles = int(user_input)
            if 20 <= fetch_candles <= 500:
                config['fetch_candles'] = fetch_candles
                print_colored(f"✅ Selected: {fetch_candles} candles for accuracy testing", "SUCCESS")
                break
            else:
                print_colored("❌ Please enter a number between 20 and 500", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Configuration cancelled", "WARNING")
            return None

    print()

    # 4. Minimum Win Rate Configuration (Optional)
    print_colored("4️⃣ MINIMUM WIN RATE THRESHOLD (OPTIONAL)", "OCEAN", bold=True)
    print_colored(f"   ⚠️  Default: {config['min_win_rate']:.0%} (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter minimum win rate (50-95%) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored(f"✅ Selected: {config['min_win_rate']:.0%} minimum win rate", "SUCCESS")
                break

            win_rate = float(user_input)
            if 50 <= win_rate <= 95:
                config['min_win_rate'] = win_rate / 100  # Convert to decimal
                print_colored(f"✅ Selected: {win_rate:.0f}% minimum win rate", "SUCCESS")
                break
            else:
                print_colored("❌ Please enter a number between 50 and 95", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Configuration cancelled", "WARNING")
            return None

    print()

    # Display final configuration
    print_colored("✅ ECHO SNIPER STRATEGY CONFIGURED:", "SUCCESS", bold=True)
    print_colored(f"   • Pattern Length: {config['pattern_length']} candles", "SUCCESS")
    print_colored(f"   • Historical Analysis: {config['historical_candles']} candles", "SUCCESS")
    print_colored(f"   • Accuracy Testing: {config['fetch_candles']} candles", "SUCCESS")
    print_colored(f"   • Minimum Win Rate: {config['min_win_rate']:.0%}", "SUCCESS")
    print()

    return config

def convert_quotex_to_oanda_pair(quotex_pair):
    """Convert Quotex pair format to Oanda format for data fetching"""
    # Remove _otc suffix
    base_pair = quotex_pair.replace("_otc", "")

    # Map Quotex pairs to Oanda pairs
    pair_mapping = {
        "EURUSD": "EUR_USD",
        "GBPUSD": "GBP_USD",
        "USDJPY": "USD_JPY",
        "AUDUSD": "AUD_USD",
        "USDCAD": "USD_CAD",
        "USDCHF": "USD_CHF",
        "NZDUSD": "NZD_USD",
        "EURGBP": "EUR_GBP",
        "EURJPY": "EUR_JPY",
        "GBPJPY": "GBP_JPY",
        "AUDJPY": "AUD_JPY",
        "EURAUD": "EUR_AUD",
        "GBPAUD": "GBP_AUD",
        "AUDCAD": "AUD_CAD",
        "AUDCHF": "AUD_CHF",
        "EURCHF": "EUR_CHF",
        "GBPCHF": "GBP_CHF",
        "GBPCAD": "GBP_CAD",
        "CADJPY": "CAD_JPY",
        "NZDJPY": "NZD_JPY",
        "EURSGD": "EUR_SGD"
    }

    return pair_mapping.get(base_pair, None)

async def fetch_quotex_market_data(asset, timeframe="M1", fetch_count=50, max_retries=3):
    """Fetch ONLY real market data - NO FALLBACK to synthetic data"""

    # STRICT POLICY: Only return real market data, never synthetic
    for attempt in range(max_retries):
        try:
            # Check if it's an OTC pair
            if "_otc" in asset:
                # For OTC pairs, use REAL PyQuotex WebSocket data ONLY
                if not quotex_client:
                    print_colored(f"❌ Quotex client not available for OTC pair {asset}", "ERROR")
                    if attempt < max_retries - 1:
                        print_colored(f"🔄 Retrying... ({attempt + 1}/{max_retries})", "WARNING")
                        await asyncio.sleep(2)
                        continue
                    return None

                # Map timeframe to period (in seconds)
                timeframe_to_seconds = {
                    "M1": 60, "M2": 120, "M5": 300, "M10": 600,
                    "M15": 900, "M30": 1800, "H1": 3600
                }
                period_seconds = timeframe_to_seconds.get(timeframe, 60)

                # Try to get real-time WebSocket data with enhanced validation
                try:

                    # Ensure Quotex client is connected
                    if not quotex_client.check_connect:
                        print_colored(f"⚠️ Quotex client not connected, attempting to reconnect...", "WARNING")
                        await quotex_client.connect()
                        await asyncio.sleep(2)

                    # Get real candle data from Quotex WebSocket
                    real_data = await quotex_client.get_candles_browser(asset, period_seconds, fetch_count)

                    if real_data and len(real_data) > 0:
                        # More lenient data validation for speed
                        min_required = max(5, min(fetch_count // 4, 10))  # Reduced requirements for faster processing

                        if len(real_data) >= min_required:
                            # Convert to DataFrame format with validation
                            df_data = []
                            valid_candles = 0

                            for candle in real_data:
                                # Validate candle data
                                if (candle.get('open', 0) > 0 and candle.get('high', 0) > 0 and
                                    candle.get('low', 0) > 0 and candle.get('close', 0) > 0):

                                    df_data.append({
                                        'open': float(candle['open']),
                                        'high': float(candle['high']),
                                        'low': float(candle['low']),
                                        'close': float(candle['close']),
                                        'volume': int(candle.get('volume', 1000)),
                                        'timestamp': candle.get('time', time.time())
                                    })
                                    valid_candles += 1

                            if valid_candles >= min_required:
                                df = pd.DataFrame(df_data)

                                # Add technical indicators
                                from utils import add_technical_indicators
                                df = add_technical_indicators(df)

                                return df
                            else:
                                # Try with whatever data we have if it's at least 3 candles
                                if valid_candles >= 3:
                                    df = pd.DataFrame(df_data)
                                    from utils import add_technical_indicators
                                    df = add_technical_indicators(df)
                                    return df
                                elif attempt < max_retries - 1:
                                    print_colored(f"⚠️ Insufficient valid candles for {asset} ({valid_candles}/{min_required}), retrying... ({attempt + 1}/{max_retries})", "WARNING")
                                    await asyncio.sleep(1)  # Reduced wait time
                                    continue
                                else:
                                    print_colored(f"❌ Failed to get sufficient valid candles for {asset} after {max_retries} attempts", "ERROR")
                                    return None
                        else:
                            # Try with whatever data we have if it's at least 3 candles
                            if len(real_data) >= 3:
                                # Process with limited data
                                df_data = []
                                for candle in real_data:
                                    if (candle.get('open', 0) > 0 and candle.get('high', 0) > 0 and
                                        candle.get('low', 0) > 0 and candle.get('close', 0) > 0):
                                        df_data.append({
                                            'open': float(candle['open']),
                                            'high': float(candle['high']),
                                            'low': float(candle['low']),
                                            'close': float(candle['close']),
                                            'volume': int(candle.get('volume', 1000)),
                                            'timestamp': candle.get('time', time.time())
                                        })

                                if len(df_data) >= 3:
                                    df = pd.DataFrame(df_data)
                                    from utils import add_technical_indicators
                                    df = add_technical_indicators(df)
                                    return df

                            if attempt < max_retries - 1:
                                print_colored(f"⚠️ Insufficient WebSocket data for {asset} ({len(real_data)}/{min_required}), retrying... ({attempt + 1}/{max_retries})", "WARNING")
                                await asyncio.sleep(1)  # Reduced wait time
                                continue
                            else:
                                print_colored(f"❌ Failed to get sufficient real data for {asset} after {max_retries} attempts", "ERROR")
                                return None
                    else:
                        if attempt < max_retries - 1:
                            print_colored(f"⚠️ No WebSocket data received for {asset}, retrying... ({attempt + 1}/{max_retries})", "WARNING")
                            await asyncio.sleep(1)  # Reduced wait time
                            continue
                        else:
                            print_colored(f"❌ No WebSocket data available for {asset} after {max_retries} attempts", "ERROR")
                            return None

                except Exception as e:
                    if attempt < max_retries - 1:
                        print_colored(f"⚠️ WebSocket data fetch failed for {asset}: {str(e)}, retrying... ({attempt + 1}/{max_retries})", "WARNING")
                        await asyncio.sleep(1)  # Reduced wait time
                        continue
                    else:
                        print_colored(f"❌ WebSocket data fetch failed for {asset} after {max_retries} attempts: {str(e)}", "ERROR")
                        return None

            else:
                # For Live pairs, use Oanda data with retries
                oanda_pair = convert_quotex_to_oanda_pair(asset)

                if not oanda_pair:
                    print_colored(f"❌ No Oanda mapping for {asset} - cannot get real data", "ERROR")
                    return None

                # Fetch live candle data from Oanda with retry logic
                df = fetch_live_candles(oanda_pair, count=fetch_count, granularity=timeframe)

                if df is not None and len(df) > 0:
                    print_colored(f"✅ Retrieved {len(df)} REAL candles for {asset} from Oanda", "SUCCESS")
                    return df
                else:
                    if attempt < max_retries - 1:
                        print_colored(f"⚠️ Failed to fetch Oanda data for {asset}, retrying... ({attempt + 1}/{max_retries})", "WARNING")
                        await asyncio.sleep(2)
                        continue
                    else:
                        print_colored(f"❌ Failed to fetch real Oanda data for {asset} after {max_retries} attempts", "ERROR")
                        return None

        except Exception as e:
            if attempt < max_retries - 1:
                print_colored(f"⚠️ Market data error for {asset}: {str(e)}, retrying... ({attempt + 1}/{max_retries})", "WARNING")
                await asyncio.sleep(2)
                continue
            else:
                print_colored(f"❌ Market data error for {asset} after {max_retries} attempts: {str(e)}", "ERROR")
                return None

    # If we reach here, all retries failed
    print_colored(f"❌ CRITICAL: Could not fetch real market data for {asset} - SKIPPING SIGNAL GENERATION", "ERROR")
    return None

async def create_comprehensive_otc_data(asset, period_seconds, candle_count=100, base_price=None):
    """DISABLED: This function creates synthetic data - NOT ALLOWED in real trading system"""
    print_colored(f"🚫 CRITICAL: Attempted to create synthetic data for {asset} - BLOCKED!", "ERROR")
    print_colored("⚠️ Real trading system only allows real market data", "ERROR")
    return None

def create_realistic_otc_data(asset):
    """DISABLED: This function creates synthetic data - NOT ALLOWED in real trading system"""
    print_colored(f"🚫 CRITICAL: Attempted to create synthetic data for {asset} - BLOCKED!", "ERROR")
    print_colored("⚠️ Real trading system only allows real market data", "ERROR")
    return None

def validate_data_quality(df, asset):
    """Validate that the data is real and not synthetic"""
    try:
        if df is None or len(df) == 0:
            return False, "No data available"

        # Check for price movement (real data should have some volatility)
        price_range = df['high'].max() - df['low'].min()
        avg_price = df['close'].mean()

        if price_range < (avg_price * 0.00001):  # Less than 0.001% movement
            return False, "No price movement detected (synthetic data)"

        # Check for realistic OHLC relationships
        invalid_candles = 0
        for _, row in df.iterrows():
            if not (row['low'] <= row['open'] <= row['high'] and
                   row['low'] <= row['close'] <= row['high']):
                invalid_candles += 1

        if invalid_candles > len(df) * 0.1:  # More than 10% invalid candles
            return False, f"Too many invalid OHLC relationships ({invalid_candles}/{len(df)})"

        # Check for data freshness if timestamp is available
        if 'timestamp' in df.columns:
            last_timestamp = df['timestamp'].iloc[-1]
            current_time = time.time()
            age_minutes = (current_time - last_timestamp) / 60

            if age_minutes > 10:  # More than 10 minutes old
                return False, f"Data is {age_minutes:.1f} minutes old"

        return True, "Data quality validated"

    except Exception as e:
        return False, f"Validation error: {str(e)}"

def create_minimal_data_for_signal(asset, current_price):
    """Create minimal realistic data for signal generation when WebSocket data is limited"""
    try:
        # Create realistic price movements around current price
        prices = []
        base_price = current_price

        # Generate 20 realistic price points
        for i in range(20):
            # Add small random movements
            change = np.random.randn() * 0.0005  # Small volatility
            base_price += change
            prices.append(base_price)

        # Create OHLC data
        data = []
        current_time = time.time()
        for i in range(len(prices) - 1):
            open_price = prices[i]
            close_price = prices[i + 1]
            high_price = max(open_price, close_price) + abs(np.random.randn() * 0.0002)
            low_price = min(open_price, close_price) - abs(np.random.randn() * 0.0002)

            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 1000 + np.random.randint(0, 500),
                'timestamp': current_time - ((len(prices) - i - 1) * 60)  # 1 minute intervals
            })

        df = pd.DataFrame(data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        return df

    except Exception as e:
        print_colored(f"❌ Error creating minimal data: {e}", "ERROR")
        return None

def calculate_envelopes(data, period=14, deviation=0.03, ma_type='sma'):
    """Calculate Envelopes indicator with SMA and deviation"""
    try:
        if len(data) < period:
            return None, None, None

        # Calculate Simple Moving Average (SMA)
        if ma_type.lower() == 'sma':
            sma_values = []
            for i in range(period - 1, len(data)):
                sma = sum(data[i - period + 1:i + 1]) / period
                sma_values.append(sma)

            if not sma_values:
                return None, None, None

            # Get latest SMA
            latest_sma = sma_values[-1]
        else:
            return None, None, None

        # Calculate envelope lines
        # Top line = SMA + (SMA * deviation)
        # Bottom line = SMA - (SMA * deviation)
        top_envelope = latest_sma + (latest_sma * deviation)
        bottom_envelope = latest_sma - (latest_sma * deviation)

        return latest_sma, top_envelope, bottom_envelope

    except Exception as e:
        return None, None, None

def detect_uptrend(data, lookback=5):
    """Fast uptrend detection - check if market is in strong uptrend"""
    try:
        if len(data) < lookback:
            return False

        recent_prices = data[-lookback:]

        # Check 1: Recent price movement (last 5 candles generally rising)
        rising_count = 0
        for i in range(1, len(recent_prices)):
            if recent_prices[i] > recent_prices[i-1]:
                rising_count += 1

        # Check 2: Strong upward momentum
        current_price = data[-1]
        start_price = recent_prices[0]

        # More conservative: Only block PUT if strong uptrend (80%+ rising candles AND significant price increase)
        strong_uptrend = (rising_count >= 4) and (current_price > start_price * 1.005)  # 0.5% increase required

        return strong_uptrend

    except Exception:
        return False

def detect_downtrend(data, lookback=5):
    """Fast downtrend detection - check if market is in strong downtrend"""
    try:
        if len(data) < lookback:
            return False

        recent_prices = data[-lookback:]

        # Check 1: Recent price movement (last 5 candles generally falling)
        falling_count = 0
        for i in range(1, len(recent_prices)):
            if recent_prices[i] < recent_prices[i-1]:
                falling_count += 1

        # Check 2: Strong downward momentum
        current_price = data[-1]
        start_price = recent_prices[0]

        # More conservative: Only block CALL if strong downtrend (80%+ falling candles AND significant price decrease)
        strong_downtrend = (falling_count >= 4) and (current_price < start_price * 0.995)  # 0.5% decrease required

        return strong_downtrend

    except Exception:
        return False

async def generate_signal(asset, strategy_engine, selected_strategies, timeframe="M1", min_candles=15, fetch_count=50, df=None):
    """Generate trading signal for asset using real market data with advanced filters to prevent small movement losses"""
    try:
        # Import advanced filters
        from advanced_filters import advanced_filters

        # Use provided data or fetch new data
        if df is None:
            df = await fetch_quotex_market_data(asset, timeframe, fetch_count)

        # STRICT CHECK: If no real data available, skip signal generation
        if df is None:
            return "hold", 0.0, 0.0, "NO_DATA", None

        if len(df) < min_candles:
            return "hold", 0.0, 0.0, "INSUFFICIENT_DATA", None

        # Validate data quality - ensure it's real market data
        is_valid, validation_msg = validate_data_quality(df, asset)
        if not is_valid:
            return "hold", 0.0, 0.0, "INVALID_DATA", None

        # Generate signal using Echo Sniper Strategy
        best_signal = "hold"
        best_confidence = 0.0
        best_strategy = "ECHO_SNIPER"

        # Use Echo Sniper strategy (only strategy available)
        signal, confidence = strategy_engine.evaluate_echo_sniper_strategy(df)

        best_confidence = confidence
        best_strategy = "ECHO_SNIPER"
        if signal == 1:
            best_signal = "call"
        elif signal == -1:
            best_signal = "put"
        else:
            best_signal = "hold"

        current_price = df['close'].iloc[-1]

        # ENHANCED UPTREND FILTER FOR PUT SIGNALS: Block PUT signals during uptrends
        if best_signal == "put" and len(df) >= 14:
            close_prices = df['close'].tolist()

            # Check 1: Envelopes filter (period=14, deviation=0.03, SMA)
            sma, top_envelope, bottom_envelope = calculate_envelopes(close_prices, period=14, deviation=0.03, ma_type='sma')

            # Check 2: Fast uptrend detection
            is_uptrend = detect_uptrend(close_prices, lookback=5)

            # Block PUT signal if either condition indicates uptrend
            if (top_envelope is not None and current_price >= top_envelope) or is_uptrend:
                best_signal = "hold"  # Convert PUT to HOLD during uptrend
                best_confidence = 0.0

        # ENHANCED DOWNTREND FILTER FOR CALL SIGNALS: Block CALL signals during downtrends
        if best_signal == "call" and len(df) >= 14:
            close_prices = df['close'].tolist()

            # Check 1: Envelopes filter (period=14, deviation=0.03, SMA)
            sma, top_envelope, bottom_envelope = calculate_envelopes(close_prices, period=14, deviation=0.03, ma_type='sma')

            # Check 2: Fast downtrend detection
            is_downtrend = detect_downtrend(close_prices, lookback=5)

            # Block CALL signal if either condition indicates downtrend
            if (bottom_envelope is not None and current_price <= bottom_envelope) or is_downtrend:
                best_signal = "hold"  # Convert CALL to HOLD during downtrend
                best_confidence = 0.0

        # CRITICAL: Apply advanced filters to prevent small movement losses (SOLVES THE MAIN PROBLEM)
        if best_signal in ["call", "put"]:
            filters_passed, filter_reasons = advanced_filters.apply_all_filters(df, asset, best_signal, timeframe)

            if not filters_passed:
                # Signal blocked by filters - convert to hold
                print_colored(f"🚫 FILTER BLOCKED {asset}: {best_signal.upper()}", "WARNING")
                for reason in filter_reasons:
                    print_colored(f"   {reason}", "WARNING")

                best_signal = "hold"
                best_confidence = 0.0

        # Extract current indicators for logging
        indicators = get_current_indicators(df)

        # Log signal if it's not hold
        signal_data = None
        if best_signal != "hold":
            # Show data retrieval confirmation (RESTORED)
            print_colored(f"✅ Retrieved {len(df)} REAL WebSocket candles for {asset}", "SUCCESS")

            # Get Echo Sniper pattern information for logging
            pattern_info = strategy_engine.get_pattern_info(df)

            # Create signal data for logging with Echo Sniper specific fields
            now = datetime.now()
            signal_data = {
                "pair": asset,
                "timestamp": now.strftime("%H:%M:%S"),
                "date": now.strftime("%Y-%m-%d"),
                "direction": best_signal,
                "price": current_price,
                "historical_candles": strategy_engine.echo_sniper_config['historical_candles'],
                "data_fetch_candles": fetch_count,
                "strategy_used": best_strategy,
                "indicators": indicators,
                "timeframe": timeframe,
                "result": "pending",
                "final_price": None,
                "evaluation_time": None,
                # Echo Sniper specific fields
                "pattern_used": pattern_info['pattern'] if pattern_info else None,
                "pattern_count": pattern_info['total_matches'] if pattern_info else 0,
                "pattern_win_rate": pattern_info['win_rate'] if pattern_info else 0.0,
                "confidence_score": best_confidence,
                "expected_direction": pattern_info['expected_direction'] if pattern_info else None,
                "pattern_length": strategy_engine.echo_sniper_config['pattern_length']
            }

            # Save signal to memory and file
            save_signal(signal_data)

        return best_signal, best_confidence, current_price, best_strategy, signal_data

    except Exception as e:
        print_colored(f"❌ Signal generation error for {asset}: {str(e)}", "ERROR")
        return "hold", 0.0, 0.0, "ERROR", None

async def run_trading_bot(account_type, is_practice_only=False):
    """Main trading bot execution with PyQuotex integration"""
    print_header(f"🚀 QUOTEX TRADING BOT - {account_type.upper()} MODE")

    # Connect to Quotex (ALWAYS connect, even for practice mode to fetch OTC data)
    connected = await connect_to_quotex(account_type)
    if not connected:
        print()
        input("Press Enter to continue...")
        return

    # Check balance (only for non-practice modes)
    if not is_practice_only:
        balance = await check_balance()
        print_colored(f"💰 Current balance: ${balance:.2f}", "SUCCESS" if balance > 0 else "ERROR")

        if balance <= 0 and account_type != "PRACTICE":
            print_colored("⚠️ Zero balance detected. Switching to practice mode...", "WARNING")
            # Switch to practice mode without calling change_account to avoid duplicate messages
            account_type = "PRACTICE"
    else:
        # Practice mode message already shown during connection - no need to repeat
        pass

    # Asset selection
    selected_assets = select_trading_pairs()
    if not selected_assets:
        return

    # Echo Sniper Strategy Configuration (moved early to define config)
    echo_sniper_config = configure_echo_sniper_strategy()
    if not echo_sniper_config:
        return

    # Timeframe selection
    duration = select_timeframe()
    if not duration:
        return

    # Echo Sniper uses its own data requirements
    # Min candles = pattern_length + historical_candles + 1 (for current candle)
    min_candles = echo_sniper_config['pattern_length'] + echo_sniper_config['historical_candles'] + 1
    fetch_count = echo_sniper_config['fetch_candles']

    # Trade time selection (before trade amount)
    trade_time = select_trade_time()

    # Set trade time immediately on Quotex site (if provided)
    if trade_time:
        time_set = await set_initial_trade_time(trade_time)
        if not time_set:
            print_colored("⚠️ Could not set trade time on site, but continuing...", "WARNING")

    # Trade amount selection
    trade_amount = select_trade_amount()
    if not trade_amount:
        return

    # Keep Trying Until Win configuration
    keep_trying_enabled, step_amounts = select_keep_trying_config()

    # Set trade amount immediately on Quotex site
    amount_set = await set_initial_trade_amount(trade_amount)
    if not amount_set:
        print_colored("⚠️ Could not set trade amount on site, but continuing...", "WARNING")

    # Initialize strategy engine with Echo Sniper configuration
    strategy_engine = StrategyEngine(echo_sniper_config)

    # Echo Sniper is the only strategy now
    selected_strategies = ["ECHO_SNIPER"]

    # Display configuration
    print()
    print_colored("📋 Trading Configuration:", "OCEAN", bold=True)
    print_colored(f"   Pairs: {', '.join(selected_assets[:3])}{'...' if len(selected_assets) > 3 else ''}", "DARK_MULBERRY")
    print_colored(f"   Timeframe: {duration//60}m", "DARK_MULBERRY")
    print_colored(f"   Min Candles: {min_candles}", "DARK_MULBERRY")
    print_colored(f"   Fetch Count: {fetch_count}", "DARK_MULBERRY")
    print_colored(f"   Strategies: {', '.join(selected_strategies[:2])}{'...' if len(selected_strategies) > 2 else ''}", "DARK_MULBERRY")
    # Display correct account type
    if is_practice_only:
        account_display = "Practice"
    elif account_type == "DEMO":
        account_display = "Demo"
    elif account_type == "REAL":
        account_display = "Live"
    else:
        account_display = account_type.title()

    print_colored(f"   Account: {account_display}", "DARK_MULBERRY")
    print_colored(f"   Amount: ${trade_amount}", "DARK_MULBERRY")
    if trade_time:
        print_colored(f"   Time: {trade_time}", "DARK_MULBERRY")
    print()

    print_colored("🎯 Starting trading bot...", "OCEAN", bold=True)
    print_colored(f"📊 Monitoring {len(selected_assets)} pair(s) with {len(selected_strategies)} strategy(ies)", "SUCCESS")

    # Initialize signal logger
    initialize_signal_logger()

    # Convert duration to timeframe for data fetching
    timeframe_map = {60: "M1", 120: "M2", 300: "M5", 600: "M10", 900: "M15", 1800: "M30", 3600: "H1"}
    granularity = timeframe_map.get(duration, "M1")

    # Calculate timeframe in minutes for proper timing
    timeframe_minutes = duration // 60

    # Keep Trying Until Win variables
    current_step = 0  # 0 = base amount, 1+ = step amounts
    consecutive_losses = 0
    base_trade_amount = trade_amount
    current_trade_amount = trade_amount
    last_set_amount = trade_amount  # Track what amount is currently set on Quotex site

    try:
        while True:
            # Calculate time to next candle based on selected timeframe
            now = datetime.now()

            # Calculate next candle time based on timeframe
            if timeframe_minutes == 1:
                # 1-minute timeframe
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            elif timeframe_minutes == 2:
                # 2-minute timeframe
                current_minute = now.minute
                next_2min = ((current_minute // 2) + 1) * 2
                if next_2min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_2min, second=0, microsecond=0)
            elif timeframe_minutes == 3:
                # 3-minute timeframe
                current_minute = now.minute
                next_3min = ((current_minute // 3) + 1) * 3
                if next_3min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_3min, second=0, microsecond=0)
            elif timeframe_minutes == 5:
                # 5-minute timeframe
                current_minute = now.minute
                next_5min = ((current_minute // 5) + 1) * 5
                if next_5min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_5min, second=0, microsecond=0)
            elif timeframe_minutes == 15:
                # 15-minute timeframe
                current_minute = now.minute
                next_15min = ((current_minute // 15) + 1) * 15
                if next_15min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_15min, second=0, microsecond=0)
            elif timeframe_minutes == 30:
                # 30-minute timeframe
                current_minute = now.minute
                next_30min = ((current_minute // 30) + 1) * 30
                if next_30min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_30min, second=0, microsecond=0)
            else:
                # Default to 1-minute for other timeframes
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)

            time_to_next = (next_candle - now).total_seconds()

            # Generate signals 2 seconds before next candle of the selected timeframe
            if time_to_next <= 2 and time_to_next > 0:
                signal_start_time = datetime.now()

                # Clean old signals first
                clear_old_signals()

                # OPTIMIZED STEP 1: Fast evaluation and Keep Trying logic for ALL pairs
                last_trade_result = None
                # Process ALL assets to evaluate their signals (not just first one)
                if selected_assets:
                    try:
                        # Evaluate signals for all pairs concurrently
                        async def evaluate_pair_signal(asset):
                            try:
                                # Ultra-fast price fetch (minimal data)
                                eval_df = await fetch_quotex_market_data(asset, granularity, 3)  # Only 3 candles
                                if eval_df is not None and len(eval_df) > 0:
                                    current_price = eval_df['close'].iloc[-1]
                                    result = evaluate_last_signal(asset, current_price)
                                    if result and result.get('result') in ['win', 'loss']:
                                        return result['result']
                                return None
                            except:
                                return None  # Silent failure for speed

                        # Evaluate all pairs concurrently
                        evaluation_tasks = [evaluate_pair_signal(asset) for asset in selected_assets]
                        evaluation_results = await asyncio.gather(*evaluation_tasks)

                        # Use the most recent result for Keep Trying logic (first non-None result)
                        for result in evaluation_results:
                            if result in ['win', 'loss']:
                                last_trade_result = result
                                break

                    except:
                        pass  # Silent failure for speed

                # Update Keep Trying amount based on last trade result
                if keep_trying_enabled and last_trade_result:
                    new_trade_amount, current_step, consecutive_losses = update_keep_trying_amount(
                        keep_trying_enabled, step_amounts, current_step, consecutive_losses,
                        base_trade_amount, last_trade_result
                    )

                    # OPTIMIZED: Only update on Quotex site if amount actually changed
                    if new_trade_amount != last_set_amount:
                        current_trade_amount = new_trade_amount
                        # Set new amount on Quotex site immediately (silent for speed)
                        amount_updated = await set_initial_trade_amount(current_trade_amount)
                        if amount_updated:
                            last_set_amount = current_trade_amount  # Cache the set amount
                            print_colored(f"💰 ${current_trade_amount}", "SUCCESS")  # Minimal message for speed
                        else:
                            print_colored(f"⚠️ Amount update failed", "WARNING")  # Minimal message
                    else:
                        current_trade_amount = new_trade_amount  # Update variable but don't call site

                # Print market scan header
                print_colored("=" * 80, "SKY_BLUE")
                print_colored(f"                      📊 MARKET SCAN - {signal_start_time.strftime('%Y-%m-%d %H:%M:%S')}", "OCEAN", bold=True)
                print_colored("=" * 80, "SKY_BLUE")

                # Print simplified table header with Keep Trying info
                header_line = (
                    f"💱 {'PAIR':<15} | "
                    f"🕐 {'TIME':<13} | "
                    f"📈📉 {'DIRECTION':<13} | "
                    f"🎯 {'CONFIDENCE':<11} | "
                    f"💰 {'PRICE':<13}"
                )

                # Keep Trying status line
                if keep_trying_enabled:
                    if current_step > 0:
                        keep_trying_status = f"🎯 Keep Trying: Step {current_step}/{len(step_amounts)} | Amount: ${current_trade_amount} | Losses: {consecutive_losses}"
                    else:
                        keep_trying_status = f"🎯 Keep Trying: Base Amount ${current_trade_amount}"
                    print_colored(keep_trying_status, "GOLD", bold=True)

                print_colored("=" * 80, "SKY_BLUE")
                print_colored(header_line, "OCEAN", bold=True)
                print_colored("=" * 80, "SKY_BLUE")

                # Collect trade messages to display outside signal box
                trade_messages = []

                # OPTIMIZED STEP 2: Fast parallel signal generation
                async def process_asset_fast(asset):
                    try:
                        signal, confidence, price, strategy, signal_data = await generate_signal(
                            asset, strategy_engine, selected_strategies, granularity, min_candles, fetch_count
                        )
                        return (asset, signal, confidence, price, strategy, None, signal_data)
                    except Exception:
                        return (asset, "hold", 0.0, 0.0, "ERROR", "PROCESSING_ERROR", None)

                # Process all assets concurrently for maximum speed
                tasks = [process_asset_fast(asset) for asset in selected_assets]
                signal_results = await asyncio.gather(*tasks)

                # Process results and execute trades
                valid_trades = []  # Collect valid trades for execution

                for asset, signal, confidence, price, strategy, error, signal_data in signal_results:
                    try:
                        if error:
                            # Silent error handling for speed
                            continue

                        # Determine signal color and display with colored circles
                        if signal == "call":
                            signal_display = "🟢 CALL"  # Green circle for CALL
                            signal_color = "SUCCESS"
                        elif signal == "put":
                            signal_display = "🔴 PUT"   # Red circle for PUT
                            signal_color = "ERROR"     # RED color for PUT signals
                        else:
                            signal_display = "⚪ HOLD"  # White circle for HOLD
                            signal_color = "SIGNAL_NOT_FOUND"

                        # Format confidence
                        conf_display = f"{confidence*100:.1f}%" if confidence > 0 else "-"

                        # Collect valid trades for ultra-fast execution
                        if not is_practice_only and signal in ["call", "put"] and confidence > 0.6:
                            valid_trades.append((asset, signal, current_trade_amount, duration))

                        # Display signal row (simplified)
                        next_candle_time = next_candle
                        row_line = (
                            f"💱 {asset:<15} | "
                            f"🕐 {next_candle_time.strftime('%H:%M:%S'):<13} | "
                            f"{signal_display:<15} | "
                            f"🎯 {conf_display:<11} | "
                            f"💰 {price:<13.5f}"
                        )

                        print_colored(row_line, signal_color)

                    except Exception as e:
                        print_colored(f"❌ Error processing {asset}: {str(e)}", "ERROR")

                # ⚡ ULTRA-FAST TRADE EXECUTION: Execute each trade on correct pair
                if valid_trades:
                    # Check balance once for all trades
                    current_balance = await check_balance()

                    # OPTIMIZED: Fast trade execution with amount validation
                    for asset, signal, amount, duration in valid_trades:
                        if current_balance >= amount:
                            # Fast amount validation before trade
                            last_set_amount = await validate_and_set_trade_amount(amount, last_set_amount)

                            # Execute trade instantly
                            success, result_msg = await execute_trade(asset, signal, amount, duration)

                            if result_msg:
                                trade_messages.append((result_msg, success))

                            # Minimal delay between trades
                            await asyncio.sleep(0.05)  # Reduced delay for speed
                        else:
                            print_colored("⚠️ Insufficient balance. Switching to practice mode...", "WARNING")
                            is_practice_only = True
                            break
                # CLEAN SIGNAL BOX: No additional messages after signal display

                print_colored("=" * 80, "SKY_BLUE")

                # Display trade messages OUTSIDE the signal box
                if trade_messages:
                    for message, is_success in trade_messages:
                        if is_success:
                            print_colored(message, "SUCCESS", bold=True)
                        else:
                            print_colored(message, "ERROR")

                # Display performance summary box
                print_summary_box(selected_assets)

                # Calculate processing time
                processing_time = (datetime.now() - signal_start_time).total_seconds()
                print_colored(f"⏳ Processing took {processing_time:.2f}s.", "SKY_BLUE")

                # Wait for next scan (with KeyboardInterrupt handling)
                try:
                    await asyncio.sleep(max(1.0, 60 - processing_time))
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt
            else:
                # Wait until it's time to generate signals (lightning-fast scanning)
                try:
                    await asyncio.sleep(0.05)  # Ultra-fast 0.05s for lightning scanning
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt

    except KeyboardInterrupt:
        # Graceful shutdown with proper cleanup - no message (handled by signal handler)
        try:
            # Close browser properly
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

        # Don't re-raise - let signal handler manage the exit
        return
    except Exception as e:
        print_colored(f"\n❌ Trading bot error: {e}", "ERROR")
        # Cleanup on error
        try:
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

async def main():
    """Main function with secure authentication"""
    # Perform authentication first
    if not auth_ui.authenticate():
        return  # Exit if authentication fails

    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-5): ").strip()

            if choice == "1":
                # Practice mode also connects to Quotex for OTC data fetching
                await run_trading_bot("PRACTICE", is_practice_only=True)
            elif choice == "2":
                await run_trading_bot("DEMO", is_practice_only=False)
            elif choice == "3":
                await run_trading_bot("REAL", is_practice_only=False)
            elif choice == "4":
                await check_quotex_balance()
            elif choice == "5":
                print_colored("👋 Thank you for using Quotex Trading Model!", "SUCCESS")
                return  # Exit completely
            else:
                print_colored("❌ Invalid choice. Please try again.", "ERROR")
                time.sleep(1)

        except KeyboardInterrupt:
            # Silent exit - message handled by signal handler
            return  # Exit completely, don't continue loop
        except Exception as e:
            print_colored(f"❌ Error: {e}", "ERROR")
            time.sleep(2)

async def cleanup_and_exit():
    """Proper cleanup function to prevent asyncio errors"""
    try:
        # Close browser and cleanup
        if quotex_client:
            if hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()

        # Cancel all tasks
        tasks = [task for task in asyncio.all_tasks() if not task.done()]
        for task in tasks:
            task.cancel()

        # Wait for cancellation
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    except:
        pass

if __name__ == "__main__":
    import signal
    import sys

    def signal_handler(sig, frame):
        """Handle Ctrl+C gracefully"""
        # Suppress unused parameter warnings
        _ = sig, frame
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
        sys.exit(0)

    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print_colored(f"❌ Unexpected error: {e}", "ERROR")
    finally:
        # Suppress all asyncio cleanup errors
        import warnings
        warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
