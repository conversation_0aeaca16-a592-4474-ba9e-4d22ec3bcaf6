import json
from collections import defaultdict

def analyze_consecutive_patterns():
    """Analyze the specific pattern of consecutive losses due to small green candles"""
    
    with open('signals/signals_2025-07-26.json', 'r') as f:
        data = json.load(f)
    
    otc_signals = [signal for signal in data if '_otc' in signal['pair']]
    
    print("🔍 ANALYZING CONSECUTIVE LOSS PATTERNS")
    print("="*60)
    
    # Group by pair and sort by timestamp
    pair_sequences = defaultdict(list)
    for signal in otc_signals:
        pair_sequences[signal['pair']].append(signal)
    
    for pair in pair_sequences:
        pair_sequences[pair].sort(key=lambda x: x['timestamp'])
    
    # Analyze consecutive losses for each pair
    total_consecutive_losses = 0
    max_consecutive = 0
    
    print("📊 CONSECUTIVE LOSS ANALYSIS BY PAIR:")
    
    for pair, signals in pair_sequences.items():
        consecutive_count = 0
        max_consecutive_pair = 0
        consecutive_sequences = []
        current_sequence = []
        
        for i, signal in enumerate(signals):
            if signal['result'] == 'loss':
                consecutive_count += 1
                current_sequence.append(signal)
                max_consecutive_pair = max(max_consecutive_pair, consecutive_count)
            else:
                if consecutive_count > 1:  # Only count sequences of 2+
                    consecutive_sequences.append(current_sequence.copy())
                    total_consecutive_losses += consecutive_count
                consecutive_count = 0
                current_sequence = []
        
        # Don't forget the last sequence if it ends with losses
        if consecutive_count > 1:
            consecutive_sequences.append(current_sequence.copy())
            total_consecutive_losses += consecutive_count
        
        max_consecutive = max(max_consecutive, max_consecutive_pair)
        
        print(f"   {pair}:")
        print(f"     Max consecutive losses: {max_consecutive_pair}")
        print(f"     Number of loss sequences: {len(consecutive_sequences)}")
        
        # Analyze the small movement pattern in consecutive losses
        small_movement_sequences = 0
        for sequence in consecutive_sequences:
            all_small_movements = True
            for signal in sequence:
                if signal.get('final_price') is not None:
                    price_change_pct = abs(signal['final_price'] - signal['price']) / signal['price'] * 100
                    if price_change_pct >= 0.05:  # Not a small movement
                        all_small_movements = False
                        break
                else:
                    all_small_movements = False
                    break
            
            if all_small_movements:
                small_movement_sequences += 1
        
        print(f"     Sequences with all small movements: {small_movement_sequences}")
        print()
    
    print(f"🎯 OVERALL PATTERN SUMMARY:")
    print(f"   Maximum consecutive losses: {max_consecutive}")
    print(f"   Total signals in consecutive loss sequences: {total_consecutive_losses}")
    print()
    
    # Show the exact problem pattern
    print("🚨 THE EXACT PROBLEM PATTERN:")
    print("   1. Market forms small green candle with upper wick")
    print("   2. Bot detects 'reversal' signal and gives PUT trade")
    print("   3. Market continues with another small green candle")
    print("   4. PUT trade results in loss")
    print("   5. Bot gives another PUT signal (reversal)")
    print("   6. Pattern repeats causing consecutive losses")
    print()
    
    # Analyze RSI behavior during consecutive losses
    print("📈 RSI BEHAVIOR IN CONSECUTIVE LOSSES:")
    
    for pair, signals in pair_sequences.items():
        consecutive_rsi = []
        current_consecutive = []
        
        for signal in signals:
            if signal['result'] == 'loss':
                current_consecutive.append(signal['indicators']['rsi'])
            else:
                if len(current_consecutive) > 1:
                    consecutive_rsi.extend(current_consecutive)
                current_consecutive = []
        
        if len(current_consecutive) > 1:
            consecutive_rsi.extend(current_consecutive)
        
        if consecutive_rsi:
            avg_rsi = sum(consecutive_rsi) / len(consecutive_rsi)
            print(f"   {pair}: Average RSI in consecutive losses: {avg_rsi:.1f}")
    
    print()
    print("🛠️  SPECIFIC CODE SOLUTIONS:")
    print()
    print("1. 📏 MINIMUM MOVEMENT FILTER:")
    print("```python")
    print("def should_trade(signal_data):")
    print("    # Calculate expected movement based on volatility")
    print("    atr = calculate_atr(historical_data)")
    print("    min_movement = atr * 0.5  # 50% of ATR")
    print("    ")
    print("    # Only trade if expected movement > threshold")
    print("    if min_movement < 0.001:  # 0.1% minimum")
    print("        return False")
    print("    return True")
    print("```")
    print()
    print("2. ⏰ CANDLE CLOSE CONFIRMATION:")
    print("```python")
    print("def wait_for_candle_close(current_time, timeframe='M1'):")
    print("    # Wait for current candle to close")
    print("    next_candle_time = get_next_candle_time(current_time, timeframe)")
    print("    time.sleep(next_candle_time - current_time)")
    print("    ")
    print("    # Re-evaluate signal after candle close")
    print("    return re_evaluate_signal()")
    print("```")
    print()
    print("3. 🚫 CONSECUTIVE LOSS PROTECTION:")
    print("```python")
    print("def check_consecutive_losses(pair, max_consecutive=2):")
    print("    recent_results = get_recent_results(pair, count=max_consecutive)")
    print("    ")
    print("    # Skip trading if too many consecutive losses")
    print("    if all(result == 'loss' for result in recent_results):")
    print("        return False")
    print("    return True")
    print("```")
    print()
    print("4. 📊 VOLATILITY CONFIRMATION:")
    print("```python")
    print("def check_volatility(data):")
    print("    # Calculate Bollinger Band width")
    print("    bb_width = (bb_upper - bb_lower) / bb_middle")
    print("    ")
    print("    # Calculate ATR")
    print("    atr = calculate_atr(data, period=14)")
    print("    ")
    print("    # Only trade if sufficient volatility")
    print("    return bb_width > 0.02 and atr > 0.001")
    print("```")
    print()
    print("🎯 IMPLEMENTATION PRIORITY:")
    print("   1. Add minimum movement filter (0.1% threshold)")
    print("   2. Implement candle close confirmation")
    print("   3. Add consecutive loss protection")
    print("   4. Include volatility filters")
    print()
    print("📈 EXPECTED RESULTS:")
    print("   - Reduce consecutive losses by 80-90%")
    print("   - Improve overall accuracy to 70-80%")
    print("   - Fewer trades but higher quality signals")

if __name__ == "__main__":
    analyze_consecutive_patterns()
