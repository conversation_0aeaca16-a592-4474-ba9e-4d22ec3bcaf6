import json
from collections import defaultdict

def load_signals_data(file_path):
    """Load signals data from JSON file"""
    with open(file_path, 'r') as f:
        data = json.load(f)
    return data

def analyze_signals(data):
    """Comprehensive analysis of signals data"""
    
    # Filter for OTC pairs only
    otc_signals = [signal for signal in data if '_otc' in signal['pair']]
    
    print(f"Total signals: {len(data)}")
    print(f"OTC signals: {len(otc_signals)}")
    print("="*60)
    
    # Overall statistics
    wins = sum(1 for signal in otc_signals if signal['result'] == 'win')
    losses = sum(1 for signal in otc_signals if signal['result'] == 'loss')
    total_otc = len(otc_signals)
    
    print(f"OVERALL OTC RESULTS:")
    print(f"Total OTC Signals: {total_otc}")
    print(f"Wins: {wins} ({wins/total_otc*100:.2f}%)")
    print(f"Losses: {losses} ({losses/total_otc*100:.2f}%)")
    print(f"Overall Accuracy: {wins/total_otc*100:.2f}%")
    print("="*60)
    
    # Analysis by pair
    pair_stats = defaultdict(lambda: {'wins': 0, 'losses': 0, 'total': 0})
    
    for signal in otc_signals:
        pair = signal['pair']
        result = signal['result']
        pair_stats[pair]['total'] += 1
        if result == 'win':
            pair_stats[pair]['wins'] += 1
        else:
            pair_stats[pair]['losses'] += 1
    
    print("ACCURACY BY PAIR:")
    for pair, stats in sorted(pair_stats.items()):
        accuracy = stats['wins'] / stats['total'] * 100
        print(f"{pair}: {stats['wins']}/{stats['total']} = {accuracy:.2f}% accuracy")
    print("="*60)
    
    # Analyze loss patterns - looking for the specific problem mentioned
    print("ANALYZING LOSS PATTERNS:")
    
    # Group signals by pair and timestamp to identify consecutive losses
    pair_sequences = defaultdict(list)
    for signal in otc_signals:
        pair_sequences[signal['pair']].append(signal)
    
    # Sort by timestamp for each pair
    for pair in pair_sequences:
        pair_sequences[pair].sort(key=lambda x: x['timestamp'])
    
    # Analyze consecutive losses and price movements
    consecutive_losses = defaultdict(list)
    small_movement_losses = []
    
    for pair, signals in pair_sequences.items():
        consecutive_count = 0
        for i, signal in enumerate(signals):
            if signal['result'] == 'loss':
                consecutive_count += 1
                
                # Check if this is a small price movement (potential small green candle issue)
                if signal.get('final_price') is not None:
                    price_change = abs(signal['final_price'] - signal['price'])
                    price_change_pct = (price_change / signal['price']) * 100
                else:
                    continue
                
                # If price change is very small but still resulted in loss
                if price_change_pct < 0.1:  # Less than 0.1% movement
                    small_movement_losses.append({
                        'pair': pair,
                        'timestamp': signal['timestamp'],
                        'direction': signal['direction'],
                        'price': signal['price'],
                        'final_price': signal['final_price'],
                        'change_pct': price_change_pct,
                        'rsi': signal['indicators']['rsi'],
                        'pattern': signal['pattern_used']
                    })
            else:
                if consecutive_count > 0:
                    consecutive_losses[pair].append(consecutive_count)
                consecutive_count = 0
    
    print(f"Small movement losses (< 0.1% price change): {len(small_movement_losses)}")
    
    # Analyze the specific problem: PUT trades losing on small upward movements
    put_small_losses = [loss for loss in small_movement_losses 
                       if loss['direction'] == 'put' and loss['final_price'] > loss['price']]
    
    call_small_losses = [loss for loss in small_movement_losses 
                        if loss['direction'] == 'call' and loss['final_price'] < loss['price']]
    
    print(f"PUT trades lost on small upward movements: {len(put_small_losses)}")
    print(f"CALL trades lost on small downward movements: {len(call_small_losses)}")
    
    # Analyze RSI patterns in these problematic trades
    if put_small_losses:
        rsi_values = [loss['rsi'] for loss in put_small_losses]
        avg_rsi = sum(rsi_values) / len(rsi_values)
        print(f"Average RSI in problematic PUT trades: {avg_rsi:.2f}")
        print(f"RSI range: {min(rsi_values):.2f} - {max(rsi_values):.2f}")
    
    print("="*60)
    
    # Calculate what accuracy would be if we filtered out small movement trades
    filtered_signals = []
    for signal in otc_signals:
        if signal.get('final_price') is not None:
            price_change_pct = abs(signal['final_price'] - signal['price']) / signal['price'] * 100
            if price_change_pct >= 0.05:  # Keep only trades with >= 0.05% movement
                filtered_signals.append(signal)
    
    if filtered_signals:
        filtered_wins = sum(1 for signal in filtered_signals if signal['result'] == 'win')
        filtered_total = len(filtered_signals)
        filtered_accuracy = filtered_wins / filtered_total * 100
        
        print("FILTERED RESULTS (removing small movements < 0.05%):")
        print(f"Remaining signals: {filtered_total} (removed {total_otc - filtered_total})")
        print(f"Wins: {filtered_wins}")
        print(f"Losses: {filtered_total - filtered_wins}")
        print(f"New accuracy: {filtered_accuracy:.2f}%")
        print(f"Improvement: +{filtered_accuracy - (wins/total_otc*100):.2f}%")
    
    print("="*60)
    
    # Analyze reversal pattern issues
    print("REVERSAL PATTERN ANALYSIS:")
    
    # Look for patterns where RSI is very low (oversold) but PUT trades still lose
    oversold_put_losses = []
    for signal in otc_signals:
        if (signal['direction'] == 'put' and 
            signal['result'] == 'loss' and 
            signal['indicators']['rsi'] < 15):  # Very oversold
            oversold_put_losses.append(signal)
    
    print(f"PUT losses in oversold conditions (RSI < 15): {len(oversold_put_losses)}")
    
    # Look for patterns where RSI is very high (overbought) but CALL trades still lose
    overbought_call_losses = []
    for signal in otc_signals:
        if (signal['direction'] == 'call' and 
            signal['result'] == 'loss' and 
            signal['indicators']['rsi'] > 85):  # Very overbought
            overbought_call_losses.append(signal)
    
    print(f"CALL losses in overbought conditions (RSI > 85): {len(overbought_call_losses)}")
    
    return {
        'total_otc': total_otc,
        'wins': wins,
        'losses': losses,
        'accuracy': wins/total_otc*100,
        'pair_stats': dict(pair_stats),
        'small_movement_losses': len(small_movement_losses),
        'put_small_losses': len(put_small_losses),
        'filtered_signals': len(filtered_signals) if filtered_signals else 0,
        'filtered_accuracy': filtered_accuracy if filtered_signals else 0
    }

def main():
    # Load and analyze the signals
    data = load_signals_data('signals/signals_2025-07-26.json')
    results = analyze_signals(data)
    
    print("\nSOLUTIONS FOR THE SMALL GREEN CANDLE PROBLEM:")
    print("1. Add minimum price movement filter (>= 0.05% or 0.1%)")
    print("2. Add volatility confirmation using ATR or Bollinger Band width")
    print("3. Wait for candle close confirmation before entering")
    print("4. Add volume confirmation if available")
    print("5. Use multiple timeframe confirmation")
    print("6. Add trend strength filter using ADX")
    print("7. Avoid trading during low volatility periods")

if __name__ == "__main__":
    main()
