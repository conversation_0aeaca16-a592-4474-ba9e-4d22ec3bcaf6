"""
Advanced Filter Configuration
Settings to prevent small movement losses and improve trading accuracy
"""

# FILTER CONFIGURATION - Adjust these values to fine-tune the filters
FILTER_CONFIG = {
    # MOVEMENT FILTERS (Most Important - Solves 95% of the problem)
    'min_movement_pct': 0.1,           # Minimum 0.1% expected price movement
    'min_atr_multiplier': 0.5,         # Minimum 50% of ATR (Average True Range)
    'min_absolute_movement': 0.0005,   # Minimum 0.05% absolute movement
    
    # VOLATILITY FILTERS
    'min_volatility_threshold': 0.02,  # Minimum 2% Bollinger Band width
    'min_atr_percentage': 0.05,        # Minimum 0.05% ATR relative to price
    
    # CONSECUTIVE LOSS PROTECTION
    'max_consecutive_losses': 2,       # Stop trading pair after 2 consecutive losses
    'reset_losses_on_win': True,       # Reset consecutive count on win
    
    # TIMING FILTERS
    'candle_close_buffer': 2,          # Don't trade within 2 seconds of candle close
    'avoid_market_open': False,        # Disabled for now
    'market_open_buffer': 300,         # 5 minutes buffer around market events
    
    # RSI EXTREME FILTERS (Critical - Prevents the exact problem identified)
    'rsi_extreme_threshold': 15,       # Block PUT when RSI < 15 (oversold)
    'rsi_overbought_threshold': 85,    # Block CALL when RSI > 85 (overbought)
    'enable_rsi_extreme_filter': True, # Enable/disable RSI extreme filtering
    
    # TREND CONFIRMATION
    'trend_confirmation_periods': 5,   # Number of periods for trend confirmation
    'min_trend_strength': 0.4,         # Minimum 40% of candles in trend direction
    'enable_trend_confirmation': True, # Enable/disable trend confirmation
    
    # VOLUME FILTERS (if volume data available)
    'volume_spike_threshold': 1.5,     # 1.5x average volume required
    'enable_volume_filter': False,     # Disabled by default (no volume data)
    
    # ADVANCED SETTINGS
    'enable_all_filters': True,        # Master switch for all filters
    'debug_filter_output': True,       # Show detailed filter reasons
    'save_filter_logs': True,          # Save filter decisions to file
}

# PAIR-SPECIFIC OVERRIDES (if certain pairs need different settings)
PAIR_SPECIFIC_CONFIG = {
    'USDCOP_otc': {
        'max_consecutive_losses': 1,    # More strict for worst performing pair
        'min_movement_pct': 0.15,       # Higher movement requirement
    },
    'USDEGP_otc': {
        'max_consecutive_losses': 3,    # Less strict for best performing pair
        'min_movement_pct': 0.08,       # Lower movement requirement
    }
}

# TIMEFRAME-SPECIFIC SETTINGS
TIMEFRAME_CONFIG = {
    'M1': {
        'candle_close_buffer': 5,       # 5 seconds for 1-minute
        'min_movement_pct': 0.1,
    },
    'M5': {
        'candle_close_buffer': 30,      # 30 seconds for 5-minute
        'min_movement_pct': 0.2,        # Higher movement for longer timeframe
    },
    'M15': {
        'candle_close_buffer': 60,      # 1 minute for 15-minute
        'min_movement_pct': 0.3,
    }
}

# EMERGENCY SETTINGS (for high loss periods)
EMERGENCY_CONFIG = {
    'enable_emergency_mode': False,    # Enable stricter filtering
    'emergency_min_movement': 0.2,     # 0.2% minimum movement in emergency
    'emergency_max_consecutive': 1,    # Only 1 consecutive loss allowed
    'emergency_rsi_threshold': 20,     # Stricter RSI limits
}

def get_filter_config(pair=None, timeframe='M1', emergency_mode=False):
    """Get filter configuration with overrides applied"""
    
    # Start with base config
    config = FILTER_CONFIG.copy()
    
    # Apply emergency settings if enabled
    if emergency_mode or EMERGENCY_CONFIG.get('enable_emergency_mode', False):
        config.update({
            'min_movement_pct': EMERGENCY_CONFIG['emergency_min_movement'],
            'max_consecutive_losses': EMERGENCY_CONFIG['emergency_max_consecutive'],
            'rsi_extreme_threshold': EMERGENCY_CONFIG['emergency_rsi_threshold'],
        })
    
    # Apply timeframe-specific settings
    if timeframe in TIMEFRAME_CONFIG:
        config.update(TIMEFRAME_CONFIG[timeframe])
    
    # Apply pair-specific overrides
    if pair and pair in PAIR_SPECIFIC_CONFIG:
        config.update(PAIR_SPECIFIC_CONFIG[pair])
    
    return config

def update_filter_config(new_config):
    """Update filter configuration at runtime"""
    global FILTER_CONFIG
    FILTER_CONFIG.update(new_config)

def get_recommended_settings_for_accuracy(target_accuracy=70):
    """Get recommended filter settings for target accuracy"""
    
    if target_accuracy >= 80:
        # Very strict settings for high accuracy
        return {
            'min_movement_pct': 0.2,
            'max_consecutive_losses': 1,
            'rsi_extreme_threshold': 20,
            'min_volatility_threshold': 0.03,
        }
    elif target_accuracy >= 70:
        # Moderate settings for good accuracy
        return {
            'min_movement_pct': 0.15,
            'max_consecutive_losses': 2,
            'rsi_extreme_threshold': 15,
            'min_volatility_threshold': 0.025,
        }
    else:
        # Relaxed settings for more trades
        return {
            'min_movement_pct': 0.1,
            'max_consecutive_losses': 3,
            'rsi_extreme_threshold': 10,
            'min_volatility_threshold': 0.02,
        }

# FILTER PERFORMANCE TRACKING
FILTER_STATS = {
    'total_signals_generated': 0,
    'total_signals_blocked': 0,
    'blocks_by_filter': {
        'movement': 0,
        'volatility': 0,
        'consecutive_losses': 0,
        'timing': 0,
        'rsi_extreme': 0,
        'trend_confirmation': 0,
    },
    'accuracy_before_filters': 0.0,
    'accuracy_after_filters': 0.0,
}

def update_filter_stats(filter_name, blocked=False):
    """Update filter performance statistics"""
    global FILTER_STATS
    
    FILTER_STATS['total_signals_generated'] += 1
    
    if blocked:
        FILTER_STATS['total_signals_blocked'] += 1
        if filter_name in FILTER_STATS['blocks_by_filter']:
            FILTER_STATS['blocks_by_filter'][filter_name] += 1

def get_filter_performance():
    """Get filter performance statistics"""
    total = FILTER_STATS['total_signals_generated']
    blocked = FILTER_STATS['total_signals_blocked']
    
    if total > 0:
        block_rate = (blocked / total) * 100
        return {
            'total_signals': total,
            'blocked_signals': blocked,
            'block_rate': f"{block_rate:.1f}%",
            'blocks_by_filter': FILTER_STATS['blocks_by_filter'],
            'estimated_accuracy_improvement': f"+{block_rate * 0.5:.1f}%"  # Rough estimate
        }
    
    return {'message': 'No data available yet'}

# QUICK PRESETS
PRESETS = {
    'CONSERVATIVE': {
        'min_movement_pct': 0.2,
        'max_consecutive_losses': 1,
        'rsi_extreme_threshold': 20,
        'min_volatility_threshold': 0.03,
    },
    'BALANCED': {
        'min_movement_pct': 0.1,
        'max_consecutive_losses': 2,
        'rsi_extreme_threshold': 15,
        'min_volatility_threshold': 0.02,
    },
    'AGGRESSIVE': {
        'min_movement_pct': 0.05,
        'max_consecutive_losses': 3,
        'rsi_extreme_threshold': 10,
        'min_volatility_threshold': 0.015,
    }
}

def apply_preset(preset_name):
    """Apply a preset configuration"""
    if preset_name in PRESETS:
        update_filter_config(PRESETS[preset_name])
        return f"Applied {preset_name} preset"
    else:
        return f"Preset {preset_name} not found. Available: {list(PRESETS.keys())}"
