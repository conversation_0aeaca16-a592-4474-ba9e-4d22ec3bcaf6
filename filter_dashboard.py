"""
Advanced Trading Filters - Real-time Monitoring Dashboard
Monitor filter performance and trading results in real-time
"""

import json
import os
import time
from datetime import datetime, timedelta
from collections import defaultdict
import sys

# Add Train Bot directory to path
sys.path.append('Train Bot')

try:
    from advanced_filters import advanced_filters
    from filter_config import get_filter_config, get_filter_performance
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class FilterDashboard:
    """Real-time monitoring dashboard for trading filters"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_update = datetime.now()
        self.session_stats = {
            'signals_generated': 0,
            'signals_blocked': 0,
            'trades_executed': 0,
            'wins': 0,
            'losses': 0,
            'consecutive_losses_prevented': 0
        }
        
    def load_today_signals(self):
        """Load today's signals from file"""
        try:
            today = datetime.now().strftime("%Y-%m-%d")
            signals_file = f"signals/signals_{today}.json"
            
            if os.path.exists(signals_file):
                with open(signals_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"❌ Error loading signals: {e}")
            return []
    
    def analyze_filter_effectiveness(self, signals):
        """Analyze how effective the filters have been"""
        
        if not signals:
            return {
                'total_signals': 0,
                'blocked_signals': 0,
                'executed_trades': 0,
                'filter_effectiveness': 0,
                'accuracy_improvement': 0
            }
        
        # Count signals by status
        total_signals = len(signals)
        executed_trades = len([s for s in signals if s.get('result') in ['win', 'loss']])
        blocked_signals = total_signals - executed_trades
        
        # Calculate win rate
        wins = len([s for s in signals if s.get('result') == 'win'])
        losses = len([s for s in signals if s.get('result') == 'loss'])
        
        current_accuracy = (wins / executed_trades * 100) if executed_trades > 0 else 0
        
        # Estimate what accuracy would have been without filters
        # Based on historical analysis: 51.5% accuracy before filters
        estimated_old_accuracy = 51.5
        accuracy_improvement = current_accuracy - estimated_old_accuracy
        
        return {
            'total_signals': total_signals,
            'blocked_signals': blocked_signals,
            'executed_trades': executed_trades,
            'wins': wins,
            'losses': losses,
            'current_accuracy': current_accuracy,
            'accuracy_improvement': accuracy_improvement,
            'block_rate': (blocked_signals / total_signals * 100) if total_signals > 0 else 0
        }
    
    def get_pair_performance(self, signals):
        """Get performance breakdown by trading pair"""
        
        pair_stats = defaultdict(lambda: {'wins': 0, 'losses': 0, 'blocked': 0, 'consecutive': 0})
        
        for signal in signals:
            pair = signal.get('pair', 'Unknown')
            result = signal.get('result')
            
            if result == 'win':
                pair_stats[pair]['wins'] += 1
            elif result == 'loss':
                pair_stats[pair]['losses'] += 1
            else:
                pair_stats[pair]['blocked'] += 1
        
        # Add consecutive losses from filter system
        for pair, consecutive in advanced_filters.consecutive_losses.items():
            if pair in pair_stats:
                pair_stats[pair]['consecutive'] = consecutive
        
        return dict(pair_stats)
    
    def display_dashboard(self):
        """Display the real-time dashboard"""
        
        # Clear screen (works on most terminals)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # Load current data
        signals = self.load_today_signals()
        filter_stats = self.analyze_filter_effectiveness(signals)
        pair_stats = self.get_pair_performance(signals)
        
        # Header
        print("🛡️  ADVANCED TRADING FILTERS - REAL-TIME DASHBOARD")
        print("="*70)
        print(f"📅 Session Start: {self.start_time.strftime('%H:%M:%S')}")
        print(f"🕐 Last Update: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️  Runtime: {datetime.now() - self.start_time}")
        print("="*70)
        
        # Filter Effectiveness
        print("\n📊 FILTER EFFECTIVENESS:")
        print(f"   Total Signals Generated: {filter_stats['total_signals']}")
        print(f"   Signals Blocked: {filter_stats['blocked_signals']} ({filter_stats['block_rate']:.1f}%)")
        print(f"   Trades Executed: {filter_stats['executed_trades']}")
        print(f"   Current Win Rate: {filter_stats['current_accuracy']:.1f}%")
        
        if filter_stats['accuracy_improvement'] > 0:
            print(f"   🎯 Accuracy Improvement: +{filter_stats['accuracy_improvement']:.1f}%")
        else:
            print(f"   📈 Accuracy Change: {filter_stats['accuracy_improvement']:.1f}%")
        
        # Trade Results
        print(f"\n💹 TRADE RESULTS:")
        print(f"   Wins: {filter_stats['wins']} ✅")
        print(f"   Losses: {filter_stats['losses']} ❌")
        
        if filter_stats['executed_trades'] > 0:
            win_rate = filter_stats['wins'] / filter_stats['executed_trades'] * 100
            print(f"   Win Rate: {win_rate:.1f}%")
        
        # Pair Performance
        if pair_stats:
            print(f"\n📈 PERFORMANCE BY PAIR:")
            for pair, stats in sorted(pair_stats.items()):
                total = stats['wins'] + stats['losses']
                if total > 0:
                    accuracy = stats['wins'] / total * 100
                    consecutive_status = f"({stats['consecutive']} consecutive losses)" if stats['consecutive'] > 0 else ""
                    print(f"   {pair}: {stats['wins']}/{total} = {accuracy:.1f}% {consecutive_status}")
                else:
                    print(f"   {pair}: No completed trades (blocked: {stats['blocked']})")
        
        # Filter Configuration
        config = get_filter_config()
        print(f"\n⚙️  CURRENT FILTER SETTINGS:")
        print(f"   Min Movement: {config['min_movement_pct']}%")
        print(f"   Max Consecutive Losses: {config['max_consecutive_losses']}")
        print(f"   RSI Extreme Threshold: {config['rsi_extreme_threshold']}")
        print(f"   Min Volatility: {config['min_volatility_threshold']}")
        
        # Recent Activity
        if signals:
            recent_signals = sorted(signals, key=lambda x: x.get('timestamp', ''), reverse=True)[:5]
            print(f"\n🕐 RECENT ACTIVITY:")
            for signal in recent_signals:
                timestamp = signal.get('timestamp', 'Unknown')
                pair = signal.get('pair', 'Unknown')
                direction = signal.get('direction', 'Unknown')
                result = signal.get('result', 'pending')
                
                status_emoji = {'win': '✅', 'loss': '❌', 'pending': '⏳'}.get(result, '❓')
                print(f"   {timestamp} - {pair} {direction.upper()} {status_emoji}")
        
        # Problem Prevention Status
        print(f"\n🚫 PROBLEM PREVENTION STATUS:")
        
        # Count how many of the original problem patterns we've prevented
        small_movement_blocks = 0
        rsi_extreme_blocks = 0
        
        for signal in signals:
            if signal.get('result') == 'pending':  # These were blocked
                # Check if it would have been a problematic trade
                if 'filters_applied' in signal:
                    filter_reasons = signal['filters_applied']
                    for reason in filter_reasons:
                        if 'movement' in reason.lower():
                            small_movement_blocks += 1
                        if 'rsi' in reason.lower() and 'extreme' in reason.lower():
                            rsi_extreme_blocks += 1
        
        print(f"   Small Movement Trades Blocked: {small_movement_blocks}")
        print(f"   Extreme RSI Trades Blocked: {rsi_extreme_blocks}")
        print(f"   Consecutive Loss Chains Broken: {len([p for p, c in advanced_filters.consecutive_losses.items() if c > 0])}")
        
        # Status Summary
        print(f"\n🎯 STATUS SUMMARY:")
        if filter_stats['executed_trades'] == 0:
            print("   ⏳ Waiting for high-quality trading opportunities...")
        elif filter_stats['current_accuracy'] > 70:
            print("   ✅ Filters working excellently! High accuracy maintained.")
        elif filter_stats['current_accuracy'] > 60:
            print("   👍 Filters working well. Good accuracy improvement.")
        else:
            print("   ⚠️  Monitor performance. Consider adjusting filter settings.")
        
        print(f"\n💡 Press Ctrl+C to exit dashboard")
    
    def run(self, refresh_interval=10):
        """Run the dashboard with automatic refresh"""
        
        print("🚀 Starting Advanced Trading Filters Dashboard...")
        print(f"📊 Refreshing every {refresh_interval} seconds")
        print("💡 Press Ctrl+C to exit")
        
        try:
            while True:
                self.display_dashboard()
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print(f"\n\n👋 Dashboard stopped. Session duration: {datetime.now() - self.start_time}")
            print("📊 Final statistics saved to filter logs.")

def main():
    """Main function to run the dashboard"""
    
    dashboard = FilterDashboard()
    
    # Check if filters are properly integrated
    try:
        config = get_filter_config()
        print("✅ Filter system loaded successfully")
        print(f"📋 Current settings: {config['min_movement_pct']}% min movement, {config['max_consecutive_losses']} max losses")
    except Exception as e:
        print(f"❌ Error loading filter system: {e}")
        return
    
    # Run dashboard
    dashboard.run(refresh_interval=10)

if __name__ == "__main__":
    main()
