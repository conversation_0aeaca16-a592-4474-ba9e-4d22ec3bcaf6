"""
Advanced Trading Filters to Prevent Small Movement Losses
Solves the small green candle problem identified in signal analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Tuple, Optional
try:
    from utils import print_colored
except ImportError:
    # Fallback if utils not available
    def print_colored(text, color="", bold=False):
        print(text)

class AdvancedTradingFilters:
    """Advanced filters to prevent small movement losses and improve accuracy"""
    
    def __init__(self):
        self.consecutive_losses = {}  # Track consecutive losses per pair
        self.recent_trades = {}       # Track recent trade history
        self.volatility_cache = {}    # Cache volatility calculations

        # Load configuration from config file
        try:
            from filter_config import get_filter_config
            self.config = get_filter_config()
        except ImportError:
            # Fallback configuration if config file not available
            self.config = {
                'min_movement_pct': 0.1,           # Minimum 0.1% expected movement
                'min_atr_multiplier': 0.5,         # Minimum 50% of ATR
                'max_consecutive_losses': 2,       # Stop after 2 consecutive losses
                'min_volatility_threshold': 0.02,  # Minimum 2% BB width
                'candle_close_buffer': 5,          # Wait 5 seconds before candle close
                'rsi_extreme_threshold': 15,       # Very oversold/overbought
                'trend_confirmation_periods': 5,   # Periods for trend confirmation
                'volume_spike_threshold': 1.5,     # 1.5x average volume
            }
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range for volatility measurement"""
        try:
            if len(df) < period + 1:
                return 0.0
            
            # Calculate True Range
            df_copy = df.copy()
            df_copy['prev_close'] = df_copy['close'].shift(1)
            
            df_copy['tr1'] = df_copy['high'] - df_copy['low']
            df_copy['tr2'] = abs(df_copy['high'] - df_copy['prev_close'])
            df_copy['tr3'] = abs(df_copy['low'] - df_copy['prev_close'])
            
            df_copy['true_range'] = df_copy[['tr1', 'tr2', 'tr3']].max(axis=1)
            
            # Calculate ATR as simple moving average of True Range
            atr = df_copy['true_range'].rolling(window=period).mean().iloc[-1]
            
            return atr if not pd.isna(atr) else 0.0
            
        except Exception as e:
            print_colored(f"❌ ATR calculation error: {e}", "ERROR")
            return 0.0
    
    def calculate_bollinger_band_width(self, df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> float:
        """Calculate Bollinger Band width as volatility indicator"""
        try:
            if len(df) < period:
                return 0.0
            
            close_prices = df['close']
            sma = close_prices.rolling(window=period).mean().iloc[-1]
            std = close_prices.rolling(window=period).std().iloc[-1]
            
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            bb_width = (upper_band - lower_band) / sma
            
            return bb_width if not pd.isna(bb_width) else 0.0
            
        except Exception as e:
            print_colored(f"❌ BB width calculation error: {e}", "ERROR")
            return 0.0
    
    def check_minimum_movement_filter(self, df: pd.DataFrame, current_price: float) -> Tuple[bool, str]:
        """Check if expected price movement meets minimum threshold"""
        try:
            # Calculate ATR-based minimum movement
            atr = self.calculate_atr(df)
            atr_based_min = atr * self.config['min_atr_multiplier']
            
            # Calculate percentage-based minimum movement
            pct_based_min = current_price * (self.config['min_movement_pct'] / 100)
            
            # Use the higher of the two minimums
            min_movement = max(atr_based_min, pct_based_min)
            
            # Check if minimum movement is reasonable
            if min_movement < (current_price * 0.0005):  # At least 0.05%
                return False, f"Expected movement too small: {min_movement:.6f} ({min_movement/current_price*100:.4f}%)"
            
            return True, f"Movement filter passed: {min_movement:.6f} ({min_movement/current_price*100:.4f}%)"
            
        except Exception as e:
            return False, f"Movement filter error: {e}"
    
    def check_volatility_filter(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """Check if market has sufficient volatility for trading"""
        try:
            # Check Bollinger Band width
            bb_width = self.calculate_bollinger_band_width(df)
            
            if bb_width < self.config['min_volatility_threshold']:
                return False, f"Low volatility: BB width {bb_width:.4f} < {self.config['min_volatility_threshold']}"
            
            # Check ATR relative to price
            atr = self.calculate_atr(df)
            current_price = df['close'].iloc[-1]
            atr_pct = (atr / current_price) * 100
            
            if atr_pct < 0.05:  # Less than 0.05% ATR
                return False, f"Low ATR: {atr_pct:.4f}% < 0.05%"
            
            return True, f"Volatility OK: BB width {bb_width:.4f}, ATR {atr_pct:.4f}%"
            
        except Exception as e:
            return False, f"Volatility filter error: {e}"
    
    def check_consecutive_losses_filter(self, pair: str) -> Tuple[bool, str]:
        """Check if pair has too many consecutive losses"""
        try:
            consecutive_count = self.consecutive_losses.get(pair, 0)
            
            if consecutive_count >= self.config['max_consecutive_losses']:
                return False, f"Too many consecutive losses: {consecutive_count} >= {self.config['max_consecutive_losses']}"
            
            return True, f"Consecutive losses OK: {consecutive_count}/{self.config['max_consecutive_losses']}"
            
        except Exception as e:
            return False, f"Consecutive losses filter error: {e}"
    
    def check_candle_close_timing(self, timeframe: str = "M1") -> Tuple[bool, str]:
        """Check if we're too close to candle close (avoid wicks)"""
        try:
            now = datetime.now()
            
            if timeframe == "M1":
                seconds_to_next = 60 - now.second
            elif timeframe == "M5":
                minutes_in_period = now.minute % 5
                seconds_to_next = (5 - minutes_in_period) * 60 - now.second
            else:
                # Default to 1-minute
                seconds_to_next = 60 - now.second
            
            if seconds_to_next <= self.config['candle_close_buffer']:
                return False, f"Too close to candle close: {seconds_to_next}s remaining"
            
            return True, f"Timing OK: {seconds_to_next}s to candle close"
            
        except Exception as e:
            return False, f"Timing filter error: {e}"
    
    def check_extreme_rsi_filter(self, df: pd.DataFrame, direction: str) -> Tuple[bool, str]:
        """Enhanced RSI filter to prevent trades in extreme oversold/overbought conditions"""
        try:
            if len(df) < 14:
                return True, "Insufficient data for RSI"
            
            # Calculate RSI
            close_prices = df['close']
            delta = close_prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1]
            
            # Check for extreme conditions that caused losses in analysis
            if direction == "put" and current_rsi < self.config['rsi_extreme_threshold']:
                # This is the exact condition that caused 87 losses in analysis
                return False, f"Extreme oversold RSI: {current_rsi:.2f} < {self.config['rsi_extreme_threshold']} (PUT blocked)"
            
            if direction == "call" and current_rsi > (100 - self.config['rsi_extreme_threshold']):
                return False, f"Extreme overbought RSI: {current_rsi:.2f} > {100 - self.config['rsi_extreme_threshold']} (CALL blocked)"
            
            return True, f"RSI OK: {current_rsi:.2f}"
            
        except Exception as e:
            return False, f"RSI filter error: {e}"
    
    def check_trend_confirmation(self, df: pd.DataFrame, direction: str) -> Tuple[bool, str]:
        """Check for trend confirmation to avoid counter-trend trades"""
        try:
            if len(df) < self.config['trend_confirmation_periods']:
                return True, "Insufficient data for trend confirmation"
            
            # Get recent closes
            recent_closes = df['close'].tail(self.config['trend_confirmation_periods'])
            
            # Calculate trend
            if direction == "call":
                # For CALL, check if we have upward momentum
                upward_candles = sum(recent_closes.diff() > 0)
                if upward_candles < 2:  # Less than 40% upward candles
                    return False, f"Weak upward trend for CALL: {upward_candles}/{self.config['trend_confirmation_periods']} up"
            
            elif direction == "put":
                # For PUT, check if we have downward momentum
                downward_candles = sum(recent_closes.diff() < 0)
                if downward_candles < 2:  # Less than 40% downward candles
                    return False, f"Weak downward trend for PUT: {downward_candles}/{self.config['trend_confirmation_periods']} down"
            
            return True, "Trend confirmation passed"
            
        except Exception as e:
            return False, f"Trend confirmation error: {e}"
    
    def apply_all_filters(self, df: pd.DataFrame, pair: str, direction: str, timeframe: str = "M1") -> Tuple[bool, List[str]]:
        """Apply all filters and return result with detailed reasons"""
        try:
            current_price = df['close'].iloc[-1]
            filter_results = []
            all_passed = True
            
            # 1. Minimum Movement Filter (Most Important)
            passed, reason = self.check_minimum_movement_filter(df, current_price)
            filter_results.append(f"Movement: {reason}")
            if not passed:
                all_passed = False
            
            # 2. Volatility Filter
            passed, reason = self.check_volatility_filter(df)
            filter_results.append(f"Volatility: {reason}")
            if not passed:
                all_passed = False
            
            # 3. Consecutive Losses Filter
            passed, reason = self.check_consecutive_losses_filter(pair)
            filter_results.append(f"Consecutive: {reason}")
            if not passed:
                all_passed = False
            
            # 4. Candle Close Timing Filter
            passed, reason = self.check_candle_close_timing(timeframe)
            filter_results.append(f"Timing: {reason}")
            if not passed:
                all_passed = False
            
            # 5. Extreme RSI Filter (Critical for preventing the identified losses)
            passed, reason = self.check_extreme_rsi_filter(df, direction)
            filter_results.append(f"RSI: {reason}")
            if not passed:
                all_passed = False
            
            # 6. Trend Confirmation Filter
            passed, reason = self.check_trend_confirmation(df, direction)
            filter_results.append(f"Trend: {reason}")
            if not passed:
                all_passed = False
            
            return all_passed, filter_results
            
        except Exception as e:
            return False, [f"Filter system error: {e}"]
    
    def update_trade_result(self, pair: str, result: str):
        """Update consecutive losses tracking"""
        try:
            if result == "loss":
                self.consecutive_losses[pair] = self.consecutive_losses.get(pair, 0) + 1
            elif result == "win":
                self.consecutive_losses[pair] = 0  # Reset on win
                
        except Exception as e:
            print_colored(f"❌ Error updating trade result: {e}", "ERROR")
    
    def get_filter_summary(self) -> Dict:
        """Get summary of current filter status"""
        return {
            'config': self.config,
            'consecutive_losses': self.consecutive_losses,
            'total_pairs_blocked': len([p for p, c in self.consecutive_losses.items() if c >= self.config['max_consecutive_losses']])
        }

# Global instance
advanced_filters = AdvancedTradingFilters()
